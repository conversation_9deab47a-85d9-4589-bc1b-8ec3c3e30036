<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;4a2aed32-d752-4414-b21b-feb862860f5b&quot;,&quot;conversations&quot;:{&quot;4a2aed32-d752-4414-b21b-feb862860f5b&quot;:{&quot;id&quot;:&quot;4a2aed32-d752-4414-b21b-feb862860f5b&quot;,&quot;createdAtIso&quot;:&quot;2025-08-05T12:05:59.987Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-05T12:23:46.376Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;d95a2dba-7790-4f58-b5a4-a5960c309602&quot;,&quot;request_message&quot;:&quot;你好。&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;你好。&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;你好！我是 Augment Agent，一个基于 Claude Sonnet 4 的 AI 编程助手。我可以帮助您处理代码库相关的任务，包括阅读、编写和修改代码。\n\n我看到您的工作区位于 `/Users/<USER>/Documents/excel-web-node`，并且您当前打开了 `server.js` 文件。\n\n请告诉我您需要什么帮助？我可以：\n- 分析和理解您的代码\n- 帮助修改或优化代码\n- 添加新功能\n- 调试问题\n- 编写测试\n- 或者其他任何与代码相关的任务\n\n您有什么具体需求吗？\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;你好。&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:19:54.719Z&quot;,&quot;response_text&quot;:&quot;你好！我是 Augment Agent，一个基于 Claude Sonnet 4 的 AI 编程助手。我可以帮助您处理代码库相关的任务，包括阅读、编写和修改代码。\n\n我看到您的工作区位于 `/Users/<USER>/Documents/excel-web-node`，并且您当前打开了 `server.js` 文件。\n\n请告诉我您需要什么帮助？我可以：\n- 分析和理解您的代码\n- 帮助修改或优化代码\n- 添加新功能\n- 调试问题\n- 编写测试\n- 或者其他任何与代码相关的任务\n\n您有什么具体需求吗？\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;4e9cf675-5e05-4cfd-9a83-66d093d6cf25&quot;,&quot;request_message&quot;:&quot;熟悉下这个项目&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;熟悉下这个项目&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_013YkYigCocAD82QjXS8hDzV&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮您熟悉这个项目。让我先查看项目的整体结构和主要文件。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;熟悉下这个项目&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:07.007Z&quot;,&quot;response_text&quot;:&quot;我来帮您熟悉这个项目。让我先查看项目的整体结构和主要文件。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;bdc0ef9c-7c3f-481a-bd87-468dfa2d93c8&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_013DhAu8Z4eEuurHZmGeLgvd&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;package.json\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看项目的核心配置文件来了解项目的基本信息：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./package-lock.json\n./package.json\n./public\n./public/css\n./public/index.html\n./public/js\n./server.js\n./uploads\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_013YkYigCocAD82QjXS8hDzV&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:12.413Z&quot;,&quot;response_text&quot;:&quot;让我查看项目的核心配置文件来了解项目的基本信息：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;8423232c-db86-4d33-a2d8-06817ad28f8d&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01HJZmjKih5viNCAaZ9fxh7z&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;server.js\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看主服务器文件：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on package.json:\n     1\t{\n     2\t  \&quot;name\&quot;: \&quot;excel-web-node\&quot;,\n     3\t  \&quot;version\&quot;: \&quot;1.0.0\&quot;,\n     4\t  \&quot;main\&quot;: \&quot;index.js\&quot;,\n     5\t  \&quot;scripts\&quot;: {\n     6\t    \&quot;test\&quot;: \&quot;echo \\\&quot;Error: no test specified\\\&quot; &amp;&amp; exit 1\&quot;\n     7\t  },\n     8\t  \&quot;keywords\&quot;: [],\n     9\t  \&quot;author\&quot;: \&quot;\&quot;,\n    10\t  \&quot;license\&quot;: \&quot;ISC\&quot;,\n    11\t  \&quot;description\&quot;: \&quot;\&quot;,\n    12\t  \&quot;dependencies\&quot;: {\n    13\t    \&quot;body-parser\&quot;: \&quot;^1.20.3\&quot;,\n    14\t    \&quot;cors\&quot;: \&quot;^2.8.5\&quot;,\n    15\t    \&quot;express\&quot;: \&quot;^4.21.2\&quot;,\n    16\t    \&quot;multer\&quot;: \&quot;^1.4.5-lts.1\&quot;,\n    17\t    \&quot;xlsx\&quot;: \&quot;^0.18.5\&quot;\n    18\t  }\n    19\t}\n    20\t\nTotal lines in file: 20\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_013DhAu8Z4eEuurHZmGeLgvd&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:16.937Z&quot;,&quot;response_text&quot;:&quot;现在让我查看主服务器文件：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ec1eeba9-c59c-415a-9aef-6692197da2e9&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01JLsQS6P7p2NSqmdwGa38mS&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;public\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看前端页面：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on server.js:\n     1\tconst express = require('express');\n     2\tconst multer = require('multer');\n     3\tconst XLSX = require('xlsx');\n     4\tconst path = require('path');\n     5\tconst fs = require('fs');\n     6\tconst os = require('os');\n     7\t\n     8\t// 创建 Express 应用\n     9\tconst app = express();\n    10\tconst port = 3000;\n    11\t\n    12\t// 配置中间件 - 增加请求体大小限制\n    13\tapp.use(express.json({ limit: '100mb' }));\n    14\tapp.use(express.urlencoded({ extended: true, limit: '100mb' }));\n    15\t\n    16\t// 添加超时设置\n    17\tapp.use((req, res, next) =&gt; {\n    18\t  // 设置较长的超时时间，避免大文件处理时请求超时\n    19\t  req.setTimeout(300000); // 5分钟\n    20\t  res.setTimeout(300000); // 5分钟\n    21\t  next();\n    22\t});\n    23\t\n    24\t// 静态文件服务\n    25\tapp.use(express.static(path.join(__dirname, 'public')));\n    26\t\n    27\t// 确保上传目录存在\n    28\tconst uploadsDir = path.join(__dirname, 'uploads');\n    29\tif (!fs.existsSync(uploadsDir)) {\n    30\t  fs.mkdirSync(uploadsDir);\n    31\t}\n    32\t\n    33\t// 配置文件上传\n    34\tconst storage = multer.diskStorage({\n    35\t  destination: function (req, file, cb) {\n    36\t    cb(null, uploadsDir);\n    37\t  },\n    38\t  filename: function (req, file, cb) {\n    39\t    cb(null, Date.now() + '-' + file.originalname);\n    40\t  }\n    41\t});\n    42\t\n    43\t// 设置 multer 配置，增加文件大小限制\n    44\tconst upload = multer({ \n    45\t  storage: storage,\n    46\t  limits: {\n    47\t    fileSize: 100 * 1024 * 1024 // 增加到100MB 限制\n    48\t  }\n    49\t});\n    50\t\n    51\t// 清理电话号码函数\n    52\tfunction cleanPhoneNumber(phone, removeCountryCode = false) {\n    53\t  if (!phone) return '';\n    54\t  \n    55\t  // 转换为字符串\n    56\t  let phoneStr = String(phone);\n    57\t  \n    58\t  // 移除所有非数字字符\n    59\t  phoneStr = phoneStr.replace(/\\D/g, '');\n    60\t  \n    61\t  // 如果需要移除国家代码（假设是+86或86开头）\n    62\t  if (removeCountryCode &amp;&amp; phoneStr.length &gt; 10) {\n    63\t    if (phoneStr.startsWith('86') &amp;&amp; phoneStr.length &gt; 11) {\n    64\t      phoneStr = phoneStr.substring(2);\n    65\t    } else if (phoneStr.length &gt; 11) {\n    66\t      // 如果长度大于11，假设前面的是国家代码\n    67\t      phoneStr = phoneStr.substring(phoneStr.length - 11);\n    68\t    }\n    69\t  }\n    70\t  \n    71\t  return phoneStr;\n    72\t}\n    73\t\n    74\t// 日期解析函数\n    75\tfunction parseDate(dateStr) {\n    76\t  if (!dateStr) return null;\n    77\t  \n    78\t  // 转换为字符串以确保一致性\n    79\t  const str = String(dateStr).trim();\n    80\t  \n    81\t  // 尝试直接解析\n    82\t  let date = new Date(str);\n    83\t  if (!isNaN(date.getTime())) {\n    84\t    return date;\n    85\t  }\n    86\t  \n    87\t  // 处理YYYY/MM/DD或YYYY-MM-DD格式\n    88\t  let match = str.match(/^(\\d{4})[\\/\\-\\.](\\d{1,2})[\\/\\-\\.](\\d{1,2})$/);\n    89\t  if (match) {\n    90\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n    91\t    return date;\n    92\t  }\n    93\t  \n    94\t  // 处理DD/MM/YYYY或DD-MM-YYYY格式\n    95\t  match = str.match(/^(\\d{1,2})[\\/\\-\\.](\\d{1,2})[\\/\\-\\.](\\d{4})$/);\n    96\t  if (match) {\n    97\t    date = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));\n    98\t    return date;\n    99\t  }\n   100\t  \n   101\t  // 处理YYYY年MM月DD日格式\n   102\t  match = str.match(/^(\\d{4})年(\\d{1,2})月(\\d{1,2})日?$/);\n   103\t  if (match) {\n   104\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n   105\t    return date;\n   106\t  }\n   107\t  \n   108\t  // 处理YYYYMMDD格式\n   109\t  match = str.match(/^(\\d{4})(\\d{2})(\\d{2})$/);\n   110\t  if (match) {\n   111\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n   112\t    return date;\n   113\t  }\n   114\t  \n   115\t  return null;\n   116\t}\n   117\t\n   118\t// 获取本机 IP 地址\n   119\tfunction getLocalIP() {\n   120\t  const nets = os.networkInterfaces();\n   121\t  for (const name of Object.keys(nets)) {\n   122\t    for (const net of nets[name]) {\n   123\t      if (net.family === 'IPv4' &amp;&amp; !net.internal) {\n   124\t        return net.address;\n   125\t      }\n   126\t    }\n   127\t  }\n   128\t  return '127.0.0.1';\n   129\t}\n   130\t// API 路由: 获取 Excel 文件的工作表列表\n   131\tapp.post('/api/get-sheets', upload.single('file'), (req, res) =&gt; {\n   132\t  console.log('收到获取工作表请求');\n   133\t  try {\n   134\t    if (!req.file) {\n   135\t      console.log('未上传文件');\n   136\t      return res.status(400).json({ error: '未上传文件' });\n   137\t    }\n   138\t    \n   139\t    console.log('文件路径:', req.file.path);\n   140\t    const filePath = req.file.path;\n   141\t    \n   142\t    // 检查文件是否存在\n   143\t    if (!fs.existsSync(filePath)) {\n   144\t      console.log('文件不存在:', filePath);\n   145\t      return res.status(400).json({ error: '文件不存在' });\n   146\t    }\n   147\t    \n   148\t    // 检查文件大小\n   149\t    const stats = fs.statSync(filePath);\n   150\t    console.log('文件大小:', stats.size, '字节');\n   151\t    \n   152\t    // 读取Excel文件 - 优化读取选项\n   153\t    const workbook = XLSX.readFile(filePath, {\n   154\t      cellFormula: false,\n   155\t      cellStyles: false,\n   156\t      cellDates: true,\n   157\t      dense: true,\n   158\t      sheetStubs: true,\n   159\t      type: 'binary',\n   160\t      rawHTML: false,\n   161\t      cellHTML: false,\n   162\t      sheetRows: 1 // 只读取第一行以获取表头，提高性能\n   163\t    });\n   164\t    \n   165\t    const sheetNames = workbook.SheetNames;\n   166\t    console.log('工作表名称:', sheetNames);\n   167\t    \n   168\t    const result = {\n   169\t      filePath: filePath,\n   170\t      sheetNames: sheetNames,\n   171\t      sheets: {}\n   172\t    };\n   173\t    \n   174\t    // 获取每个工作表的列头信息\n   175\t    sheetNames.forEach(sheetName =&gt; {\n   176\t      const worksheet = workbook.Sheets[sheetName];\n   177\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   178\t        header: 1, \n   179\t        defval: '',\n   180\t        range: 0,\n   181\t        blankrows: false,\n   182\t        sheetRows: 1 // 只读取第一行以获取表头\n   183\t      });\n   184\t      \n   185\t      if (jsonData.length &gt; 0) {\n   186\t        result.sheets[sheetName] = {\n   187\t          headers: jsonData[0]\n   188\t        };\n   189\t      } else {\n   190\t        result.sheets[sheetName] = {\n   191\t          headers: []\n   192\t        };\n   193\t      }\n   194\t    });\n   195\t    \n   196\t    console.log('成功获取工作表信息');\n   197\t    res.json(result);\n   198\t  } catch (error) {\n   199\t    console.error('处理Excel文件出错:', error);\n   200\t    res.status(500).json({ error: error.message });\n   201\t  }\n   202\t});\n   203\t\n   204\t// API 路由: 电话号码去重\n   205\tapp.post('/api/dedup-phones', (req, res) =&gt; {\n   206\t  console.log('收到去重请求:', req.body);\n   207\t  try {\n   208\t    const { filePath, sheetName, phoneColumnIndex, removeCountryCode, keepOriginalFormat, dateFilter } = req.body;\n   209\t    \n   210\t    if (!filePath || !sheetName) {\n   211\t      return res.status(400).json({ error: '缺少必要参数' });\n   212\t    }\n   213\t    \n   214\t    // 检查文件是否存在\n   215\t    if (!fs.existsSync(filePath)) {\n   216\t      return res.status(400).json({ error: '文件不存在' });\n   217\t    }\n   218\t    \n   219\t    // 读取Excel文件 - 使用优化的读取选项\n   220\t    const options = {\n   221\t      cellFormula: false,\n   222\t      cellStyles: false,\n   223\t      cellDates: true,\n   224\t      dense: true,\n   225\t      sheetStubs: true,\n   226\t      type: 'binary',\n   227\t      rawHTML: false,\n   228\t      cellHTML: false\n   229\t    };\n   230\t    \n   231\t    const workbook = XLSX.readFile(filePath, options);\n   232\t    \n   233\t    if (!workbook.SheetNames.includes(sheetName)) {\n   234\t      return res.status(400).json({ error: '工作表不存在' });\n   235\t    }\n   236\t    \n   237\t    // 获取工作表\n   238\t    const worksheet = workbook.Sheets[sheetName];\n   239\t    \n   240\t    // 获取表头\n   241\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   242\t      header: 1, \n   243\t      defval: '',\n   244\t      blankrows: false,\n   245\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   246\t    })[0] || [];\n   247\t    \n   248\t    // 分批处理数据\n   249\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   250\t    const uniquePhones = new Map();\n   251\t    const duplicates = new Map();\n   252\t    let totalProcessed = 0;\n   253\t    \n   254\t    // 获取工作表范围\n   255\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   256\t    const totalRows = range.e.r;\n   257\t    \n   258\t    // 分批处理数据\n   259\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   260\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   261\t      \n   262\t      // 读取当前批次的数据\n   263\t      const batchRange = {\n   264\t        s: { r: startRow, c: 0 },\n   265\t        e: { r: endRow, c: range.e.c }\n   266\t      };\n   267\t      \n   268\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   269\t        header: 1, \n   270\t        defval: '',\n   271\t        blankrows: false,\n   272\t        range: batchRange\n   273\t      });\n   274\t      \n   275\t      // 应用日期筛选\n   276\t      let filteredData = jsonData;\n   277\t      if (dateFilter &amp;&amp; dateFilter.enabled &amp;&amp; dateFilter.columnIndex &gt;= 0) {\n   278\t        filteredData = jsonData.filter(row =&gt; {\n   279\t          if (dateFilter.columnIndex &gt;= row.length || !row[dateFilter.columnIndex]) {\n   280\t            return false;\n   281\t          }\n   282\t          \n   283\t          const date = parseDate(row[dateFilter.columnIndex]);\n   284\t          if (!date) return false;\n   285\t          \n   286\t          const year = date.getFullYear();\n   287\t          const month = date.getMonth() + 1;\n   288\t          \n   289\t          let match = true;\n   290\t          if (dateFilter.year &amp;&amp; dateFilter.year !== '') {\n   291\t            const filterYear = parseInt(dateFilter.year);\n   292\t            if (year !== filterYear) {\n   293\t              match = false;\n   294\t            }\n   295\t          }\n   296\t          \n   297\t          if (match &amp;&amp; dateFilter.month &amp;&amp; dateFilter.month !== '') {\n   298\t            const filterMonth = parseInt(dateFilter.month);\n   299\t            if (month !== filterMonth) {\n   300\t              match = false;\n   301\t            }\n   302\t          }\n   303\t          \n   304\t          return match;\n   305\t        });\n   306\t      }\n   307\t      \n   308\t      // 处理当前批次的数据\n   309\t      filteredData.forEach((row, index) =&gt; {\n   310\t        if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) return;\n   311\t        \n   312\t        const originalPhone = String(row[phoneColumnIndex]);\n   313\t        const cleanedPhone = cleanPhoneNumber(originalPhone, removeCountryCode);\n   314\t        \n   315\t        if (!cleanedPhone) return;\n   316\t        \n   317\t        if (uniquePhones.has(cleanedPhone)) {\n   318\t          // 记录重复项\n   319\t          if (!duplicates.has(cleanedPhone)) {\n   320\t            duplicates.set(cleanedPhone, [uniquePhones.get(cleanedPhone)]);\n   321\t          }\n   322\t          duplicates.get(cleanedPhone).push(startRow + index);\n   323\t        } else {\n   324\t          // 记录唯一项\n   325\t          uniquePhones.set(cleanedPhone, startRow + index);\n   326\t        }\n   327\t      });\n   328\t      \n   329\t      totalProcessed += filteredData.length;\n   330\t    }\n   331\t    \n   332\t    // 构建结果\n   333\t    const uniqueRows = [];\n   334\t    const duplicateRows = [];\n   335\t    \n   336\t    // 再次分批读取数据，获取完整行\n   337\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   338\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   339\t      \n   340\t      // 读取当前批次的数据\n   341\t      const batchRange = {\n   342\t        s: { r: startRow, c: 0 },\n   343\t        e: { r: endRow, c: range.e.c }\n   344\t      };\n   345\t      \n   346\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   347\t        header: 1, \n   348\t        defval: '',\n   349\t        blankrows: false,\n   350\t        range: batchRange\n   351\t      });\n   352\t      \n   353\t      // 处理当前批次的数据\n   354\t      jsonData.forEach((row, index) =&gt; {\n   355\t        const rowIndex = startRow + index;\n   356\t        \n   357\t        // 检查是否是唯一行\n   358\t        for (const [phone, uniqueIndex] of uniquePhones.entries()) {\n   359\t          if (uniqueIndex === rowIndex) {\n   360\t            const newRow = [...row];\n   361\t            \n   362\t            // 如果需要保持原始格式，不修改电话号码\n   363\t            if (!keepOriginalFormat) {\n   364\t              newRow[phoneColumnIndex] = phone;\n   365\t            }\n   366\t            \n   367\t            uniqueRows.push(newRow);\n   368\t            break;\n   369\t          }\n   370\t        }\n   371\t        \n   372\t        // 检查是否是重复行\n   373\t        for (const [phone, indices] of duplicates.entries()) {\n   374\t          if (indices.includes(rowIndex)) {\n   375\t            duplicateRows.push(row);\n   376\t            break;\n   377\t          }\n   378\t        }\n   379\t      });\n   380\t      \n   381\t      // 限制结果数量，避免内存溢出\n   382\t      if (uniqueRows.length &gt; 50000 || duplicateRows.length &gt; 50000) {\n   383\t        console.log('结果数量过多，提前结束处理');\n   384\t        break;\n   385\t      }\n   386\t    }\n   387\t    \n   388\t    console.log(`去重完成: 唯一号码 ${uniqueRows.length} 个, 重复号码 ${duplicateRows.length} 个`);\n   389\t    res.json({\n   390\t      headers: headers,\n   391\t      uniqueRows: uniqueRows,\n   392\t      duplicateRows: duplicateRows,\n   393\t      totalProcessed: totalProcessed\n   394\t    });\n   395\t  } catch (error) {\n   396\t    console.error('去重处理出错:', error);\n   397\t    res.status(500).json({ error: error.message });\n   398\t  }\n   399\t});\n   400\t\n   401\t// API 路由: 关键词搜索\n   402\tapp.post('/api/search-excel', (req, res) =&gt; {\n   403\t  console.log('收到搜索请求:', req.body);\n   404\t  try {\n   405\t    const { filePath, sheetName, searchColumnIndex, keyword, caseSensitive, exactMatch, deduplicateResults, phoneColumnIndex } = req.body;\n   406\t    \n   407\t    if (!filePath || !sheetName) {\n   408\t      return res.status(400).json({ error: '缺少必要参数' });\n   409\t    }\n   410\t    \n   411\t    // 读取Excel文件 - 优化读取选项\n   412\t    const workbook = XLSX.readFile(filePath, {\n   413\t      cellFormula: false,\n   414\t      cellStyles: false,\n   415\t      cellDates: true,\n   416\t      dense: true,\n   417\t      sheetStubs: true,\n   418\t      type: 'binary',\n   419\t      rawHTML: false,\n   420\t      cellHTML: false\n   421\t    });\n   422\t    \n   423\t    if (!workbook.SheetNames.includes(sheetName)) {\n   424\t      return res.status(400).json({ error: '工作表不存在' });\n   425\t    }\n   426\t    \n   427\t    // 获取工作表\n   428\t    const worksheet = workbook.Sheets[sheetName];\n   429\t    \n   430\t    // 获取表头\n   431\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   432\t      header: 1, \n   433\t      defval: '',\n   434\t      blankrows: false,\n   435\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   436\t    })[0] || [];\n   437\t    \n   438\t    // 准备搜索关键词\n   439\t    let searchKeyword = keyword;\n   440\t    if (!caseSensitive) {\n   441\t      searchKeyword = searchKeyword.toLowerCase();\n   442\t    }\n   443\t    \n   444\t    // 分批处理数据\n   445\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   446\t    const matchedRows = [];\n   447\t    const uniquePhones = new Set();\n   448\t    \n   449\t    // 获取工作表范围\n   450\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   451\t    const totalRows = range.e.r;\n   452\t    \n   453\t    // 分批处理数据\n   454\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   455\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   456\t      \n   457\t      // 读取当前批次的数据\n   458\t      const batchRange = {\n   459\t        s: { r: startRow, c: 0 },\n   460\t        e: { r: endRow, c: range.e.c }\n   461\t      };\n   462\t      \n   463\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   464\t        header: 1, \n   465\t        defval: '',\n   466\t        blankrows: false,\n   467\t        range: batchRange\n   468\t      });\n   469\t      \n   470\t      // 搜索匹配的行\n   471\t      jsonData.forEach(row =&gt; {\n   472\t        if (searchColumnIndex &gt;= row.length || !row[searchColumnIndex]) return;\n   473\t        \n   474\t        let cellValue = String(row[searchColumnIndex]);\n   475\t        if (!caseSensitive) {\n   476\t          cellValue = cellValue.toLowerCase();\n   477\t        }\n   478\t        \n   479\t        let isMatch = false;\n   480\t        if (exactMatch) {\n   481\t          isMatch = cellValue === searchKeyword;\n   482\t        } else {\n   483\t          isMatch = cellValue.includes(searchKeyword);\n   484\t        }\n   485\t        \n   486\t        if (isMatch) {\n   487\t          // 如果需要去重\n   488\t          if (deduplicateResults &amp;&amp; phoneColumnIndex !== undefined &amp;&amp; phoneColumnIndex &gt;= 0) {\n   489\t            if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) {\n   490\t              matchedRows.push(row);\n   491\t              return;\n   492\t            }\n   493\t            \n   494\t            const phone = cleanPhoneNumber(row[phoneColumnIndex], false);\n   495\t            if (!phone) {\n   496\t              matchedRows.push(row);\n   497\t              return;\n   498\t            }\n   499\t            \n   500\t            if (uniquePhones.has(phone)) {\n   501\t              return;\n   502\t            }\n   503\t            \n   504\t            uniquePhones.add(phone);\n   505\t          }\n   506\t          \n   507\t          matchedRows.push(row);\n   508\t          \n   509\t          // 限制结果数量，避免内存溢出\n   510\t          if (matchedRows.length &gt;= 10000) {\n   511\t            return;\n   512\t          }\n   513\t        }\n   514\t      });\n   515\t      \n   516\t      // 如果已经找到足够多的结果，提前结束\n   517\t      if (matchedRows.length &gt;= 10000) {\n   518\t        console.log('搜索结果超过10000条，提前结束搜索');\n   519\t        break;\n   520\t      }\n   521\t    }\n   522\t    \n   523\t    console.log(`搜索完成: 找到 ${matchedRows.length} 条匹配记录`);\n   524\t    res.json({\n   525\t      headers: headers,\n   526\t      matchedRows: matchedRows\n   527\t    });\n   528\t  } catch (error) {\n   529\t    console.error('搜索出错:', error);\n   530\t    res.status(500).json({ error: error.message });\n   531\t  }\n   532\t});\n   533\t\n   534\t// API 路由: 多电话号码分离\n   535\tapp.post('/api/split-phones', (req, res) =&gt; {\n   536\t  console.log('收到分离请求:', req.body);\n   537\t  try {\n   538\t    const { filePath, sheetName, phoneColumnIndex, nameColumnIndex, removeCountryCode, deduplicateResults, dateFilter } = req.body;\n   539\t    \n   540\t    if (!filePath || !sheetName) {\n   541\t      return res.status(400).json({ error: '缺少必要参数' });\n   542\t    }\n   543\t    \n   544\t    // 检查文件是否存在\n   545\t    if (!fs.existsSync(filePath)) {\n   546\t      console.log('文件不存在:', filePath);\n   547\t      return res.status(400).json({ error: '文件不存在' });\n   548\t    }\n   549\t    \n   550\t    // 读取Excel文件 - 优化读取选项\n   551\t    const workbook = XLSX.readFile(filePath, {\n   552\t      cellFormula: false,\n   553\t      cellStyles: false,\n   554\t      cellDates: true,\n   555\t      dense: true,\n   556\t      sheetStubs: true,\n   557\t      type: 'binary',\n   558\t      rawHTML: false,\n   559\t      cellHTML: false\n   560\t    });\n   561\t    \n   562\t    if (!workbook.SheetNames.includes(sheetName)) {\n   563\t      return res.status(400).json({ error: '工作表不存在' });\n   564\t    }\n   565\t    \n   566\t    // 获取工作表\n   567\t    const worksheet = workbook.Sheets[sheetName];\n   568\t    \n   569\t    // 获取表头\n   570\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   571\t      header: 1, \n   572\t      defval: '',\n   573\t      blankrows: false,\n   574\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   575\t    })[0] || [];\n   576\t    \n   577\t    // 分批处理数据\n   578\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   579\t    const splitRows = [];\n   580\t    const uniquePhones = new Set();\n   581\t    let totalProcessed = 0;\n   582\t    \n   583\t    // 获取工作表范围\n   584\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   585\t    const totalRows = range.e.r;\n   586\t    \n   587\t    console.log(`处理 ${totalRows} 行数据进行电话号码分离`);\n   588\t    \n   589\t    // 分批处理数据\n   590\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   591\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   592\t      \n   593\t      // 读取当前批次的数据\n   594\t      const batchRange = {\n   595\t        s: { r: startRow, c: 0 },\n   596\t        e: { r: endRow, c: range.e.c }\n   597\t      };\n   598\t      \n   599\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   600\t        header: 1, \n   601\t        defval: '',\n   602\t        blankrows: false,\n   603\t        range: batchRange\n   604\t      });\n   605\t      \n   606\t      // 应用日期筛选\n   607\t      let filteredData = jsonData;\n   608\t      if (dateFilter &amp;&amp; dateFilter.enabled &amp;&amp; dateFilter.columnIndex &gt;= 0) {\n   609\t        filteredData = jsonData.filter(row =&gt; {\n   610\t          if (dateFilter.columnIndex &gt;= row.length || !row[dateFilter.columnIndex]) {\n   611\t            return false;\n   612\t          }\n   613\t          \n   614\t          const date = parseDate(row[dateFilter.columnIndex]);\n   615\t          if (!date) return false;\n   616\t          \n   617\t          const year = date.getFullYear();\n   618\t          const month = date.getMonth() + 1;\n   619\t          \n   620\t          let match = true;\n   621\t          if (dateFilter.year &amp;&amp; dateFilter.year !== '') {\n   622\t            const filterYear = parseInt(dateFilter.year);\n   623\t            if (year !== filterYear) {\n   624\t              match = false;\n   625\t            }\n   626\t          }\n   627\t          \n   628\t          if (match &amp;&amp; dateFilter.month &amp;&amp; dateFilter.month !== '') {\n   629\t            const filterMonth = parseInt(dateFilter.month);\n   630\t            if (month !== filterMonth) {\n   631\t              match = false;\n   632\t            }\n   633\t          }\n   634\t          \n   635\t          return match;\n   636\t        });\n   637\t      }\n   638\t      \n   639\t      totalProcessed += filteredData.length;\n   640\t      \n   641\t      // 分离电话号码\n   642\t      filteredData.forEach(row =&gt; {\n   643\t        if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) return;\n   644\t        \n   645\t        const phoneStr = String(row[phoneColumnIndex]);\n   646\t        \n   647\t        // 使用多种分隔符分割电话号码\n   648\t        const phones = phoneStr.split(/[,;，；\\s\\/\\|\\n\\r]+/);\n   649\t        \n   650\t        // 处理可能连在一起的多个电话号码\n   651\t        let processedPhones = [];\n   652\t        phones.forEach(phone =&gt; {\n   653\t          // 如果一个字段包含多个11位数字，可能是多个电话号码连在一起\n   654\t          if (phone.length &gt; 11) {\n   655\t            // 尝试按照11位一组分割\n   656\t            const cleanedLongPhone = phone.replace(/\\D/g, '');\n   657\t            if (cleanedLongPhone.length &gt;= 11) {\n   658\t              // 每11位分割一次\n   659\t              for (let i = 0; i &lt; cleanedLongPhone.length; i += 11) {\n   660\t                if (i + 11 &lt;= cleanedLongPhone.length) {\n   661\t                  const singlePhone = cleanedLongPhone.substring(i, i + 11);\n   662\t                  processedPhones.push(singlePhone);\n   663\t                } else if (cleanedLongPhone.length - i &gt;= 8) {\n   664\t                  // 如果剩余数字大于等于8位，也认为是一个有效电话号码\n   665\t                  const singlePhone = cleanedLongPhone.substring(i);\n   666\t                  processedPhones.push(singlePhone);\n   667\t                }\n   668\t              }\n   669\t            } else {\n   670\t              processedPhones.push(phone);\n   671\t            }\n   672\t          } else {\n   673\t            processedPhones.push(phone);\n   674\t          }\n   675\t        });\n   676\t        \n   677\t        processedPhones.forEach(phone =&gt; {\n   678\t          if (!phone) return;\n   679\t          \n   680\t          const cleanedPhone = cleanPhoneNumber(phone, removeCountryCode);\n   681\t          if (!cleanedPhone || cleanedPhone.length &lt; 8) {\n   682\t            return; // 跳过无效电话号码\n   683\t          }\n   684\t          \n   685\t          // 如果需要去重并且电话号码已存在，则跳过\n   686\t          if (deduplicateResults &amp;&amp; uniquePhones.has(cleanedPhone)) {\n   687\t            return;\n   688\t          }\n   689\t          \n   690\t          // 创建新行\n   691\t          const newRow = [...row];\n   692\t          newRow[phoneColumnIndex] = cleanedPhone;\n   693\t          \n   694\t          splitRows.push(newRow);\n   695\t          \n   696\t          if (deduplicateResults) {\n   697\t            uniquePhones.add(cleanedPhone);\n   698\t          }\n   699\t          \n   700\t          // 限制结果数量，避免内存溢出\n   701\t          if (splitRows.length &gt;= 100000) {\n   702\t            return;\n   703\t          }\n   704\t        });\n   705\t        \n   706\t        // 如果已经找到足够多的结果，提前结束\n   707\t        if (splitRows.length &gt;= 100000) {\n   708\t          return;\n   709\t        }\n   710\t      });\n   711\t      \n   712\t      // 如果已经找到足够多的结果，提前结束\n   713\t      if (splitRows.length &gt;= 100000) {\n   714\t        console.log('分离结果超过100000条，提前结束处理');\n   715\t        break;\n   716\t      }\n   717\t    }\n   718\t    \n   719\t    console.log(`分离完成: 共生成 ${splitRows.length} 行数据`);\n   720\t    res.json({\n   721\t      headers: headers,\n   722\t      splitRows: splitRows,\n   723\t      totalProcessed: totalProcessed\n   724\t    });\n   725\t  } catch (error) {\n   726\t    console.error('分离电话号码出错:', error);\n   727\t    res.status(500).json({ error: error.message });\n   728\t  }\n   729\t});\n   730\t\n   731\t// API 路由: 导出 Excel\n   732\tapp.post('/api/export-excel', (req, res) =&gt; {\n   733\t    console.log('收到导出请求');\n   734\t    try {\n   735\t      const { headers, data, sheetName } = req.body;\n   736\t      \n   737\t      if (!headers || !data) {\n   738\t        return res.status(400).json({ error: '缺少必要参数' });\n   739\t      }\n   740\t      \n   741\t      // 限制导出数据量\n   742\t      const MAX_EXPORT_ROWS = 100000;\n   743\t      const limitedData = data.length &gt; MAX_EXPORT_ROWS ? data.slice(0, MAX_EXPORT_ROWS) : data;\n   744\t      \n   745\t      if (data.length &gt; MAX_EXPORT_ROWS) {\n   746\t        console.log(`警告: 导出数据被限制为 ${MAX_EXPORT_ROWS} 行，原始数据有 ${data.length} 行`);\n   747\t      }\n   748\t      \n   749\t      // 创建工作簿\n   750\t      const workbook = XLSX.utils.book_new();\n   751\t      \n   752\t      // 准备数据（包括表头）\n   753\t      const wsData = [headers, ...limitedData];\n   754\t      \n   755\t      // 创建工作表\n   756\t      const worksheet = XLSX.utils.aoa_to_sheet(wsData);\n   757\t      \n   758\t      // 将工作表添加到工作簿\n   759\t      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName || 'Sheet1');\n   760\t      \n   761\t      // 生成临时文件路径\n   762\t      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);\n   763\t      \n   764\t      // 写入文件\n   765\t      XLSX.writeFile(workbook, outputPath, { compression: true }); // 启用压缩\n   766\t      \n   767\t      // 检查文件是否成功创建\n   768\t      if (!fs.existsSync(outputPath)) {\n   769\t        throw new Error('创建Excel文件失败');\n   770\t      }\n   771\t      \n   772\t      // 检查文件大小\n   773\t      const stats = fs.statSync(outputPath);\n   774\t      if (stats.size === 0) {\n   775\t        throw new Error('生成的Excel文件为空');\n   776\t      }\n   777\t      \n   778\t      console.log(`Excel文件已创建: ${outputPath}, 大小: ${stats.size} 字节`);\n   779\t      \n   780\t      // 设置响应头，让浏览器下载文件\n   781\t      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\n   782\t      res.setHeader('Content-Disposition', `attachment; filename=\&quot;${encodeURIComponent(sheetName || 'export')}.xlsx\&quot;`);\n   783\t      res.setHeader('Content-Length', stats.size);\n   784\t      \n   785\t      // 创建文件读取流\n   786\t      const fileStream = fs.createReadStream(outputPath);\n   787\t      \n   788\t      // 处理\n   789\t      fileStream.on('error', (error) =&gt; {\n   790\t        console.error('文件流错误:', error);\n   791\t        // 如果响应头尚未发送，发送错误响应\n   792\t        if (!res.headersSent) {\n   793\t          res.status(500).json({ error: '读取导出文件时出错' });\n   794\t        } else {\n   795\t          // 否则结束响应\n   796\t          res.end();\n   797\t        }\n   798\t        \n   799\t        // 尝试删除临时文件\n   800\t        try {\n   801\t          fs.unlinkSync(outputPath);\n   802\t        } catch (unlinkError) {\n   803\t          console.error('删除临时文件出错:', unlinkError);\n   804\t        }\n   805\t      });\n   806\t      \n   807\t      // 管道连接到响应\n   808\t      fileStream.pipe(res).on('finish', () =&gt; {\n   809\t        console.log('文件传输完成');\n   810\t        \n   811\t        // 删除临时文件\n   812\t        fs.unlink(outputPath, (unlinkErr) =&gt; {\n   813\t          if (unlinkErr) {\n   814\t            console.error('删除临时文件出错:', unlinkErr);\n   815\t          }\n   816\t        });\n   817\t      });\n   818\t    } catch (error) {\n   819\t      console.error('导出Excel出错:', error);\n   820\t      res.status(500).json({ error: error.message });\n   821\t      \n   822\t      // 尝试删除可能创建的临时文件\n   823\t      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);\n   824\t      if (fs.existsSync(outputPath)) {\n   825\t        try {\n   826\t          fs.unlinkSync(outputPath);\n   827\t        } catch (unlinkError) {\n   828\t          console.error('删除临时文件出错:', unlinkError);\n   829\t        }\n   830\t      }\n   831\t    }\n   832\t  });\n   833\t\n   834\t// 启动服务器\n   835\tapp.listen(port, () =&gt; {\n   836\t  const localIP = getLocalIP();\n   837\t  console.log(`服务器已启动:`);\n   838\t  console.log(`- 本地访问: http://localhost:${port}`);\n   839\t  console.log(`- 局域网访问: http://${localIP}:${port}`);\n   840\t});\nTotal lines in file: 840\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01HJZmjKih5viNCAaZ9fxh7z&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:21.106Z&quot;,&quot;response_text&quot;:&quot;现在让我查看前端页面：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;61058cc5-056b-4172-935b-e7fa917e02fd&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01J2HZh1XZDsDCmjJxeE6GEh&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;public/index.html\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [1, 50]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in public, excluding hidden items:\npublic/css\npublic/css/bootstrap\npublic/css/style.css\npublic/index.html\npublic/js\npublic/js/bootstrap\npublic/js/error-handler.js\npublic/js/main.js\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01JLsQS6P7p2NSqmdwGa38mS&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:26.207Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;b0d7c840-f3a9-4a8a-a4b8-0a953ff33ab0&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01Cbnk5gj8qMh6JuqqQHuCDj&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;public/js/main.js\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [1, 100]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看主要的JavaScript文件：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on public/index.html:\n     1\t&lt;!DOCTYPE html&gt;\n     2\t&lt;html lang=\&quot;zh-CN\&quot;&gt;\n     3\t&lt;head&gt;\n     4\t  &lt;meta charset=\&quot;UTF-8\&quot;&gt;\n     5\t  &lt;meta name=\&quot;viewport\&quot; content=\&quot;width=device-width, initial-scale=1.0\&quot;&gt;\n     6\t  &lt;title&gt;Excel 电话号码处理工具&lt;/title&gt;\n     7\t  &lt;script src=\&quot;/js/error-handler.js\&quot;&gt;&lt;/script&gt;\n     8\t  &lt;!-- 使用本地 Bootstrap CSS --&gt;\n     9\t  &lt;link rel=\&quot;stylesheet\&quot; href=\&quot;/css/bootstrap/bootstrap.min.css\&quot;&gt;\n    10\t  &lt;link rel=\&quot;stylesheet\&quot; href=\&quot;/css/style.css\&quot;&gt;\n    11\t  &lt;!-- 添加 favicon 链接 --&gt;\n    12\t  &lt;link rel=\&quot;icon\&quot; href=\&quot;data:,\&quot;&gt;\n    13\t  &lt;!-- 内联样式，确保基本样式即使外部CSS加载失败也能显示 --&gt;\n    14\t  &lt;style&gt;\n    15\t    body {\n    16\t      background-color: #f8f9fa;\n    17\t      font-family: Arial, sans-serif;\n    18\t      padding: 20px;\n    19\t    }\n    20\t    .container {\n    21\t      max-width: 1200px;\n    22\t      margin: 0 auto;\n    23\t    }\n    24\t    .card {\n    25\t      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    26\t      border: none;\n    27\t      margin-bottom: 20px;\n    28\t      background-color: white;\n    29\t      border-radius: 5px;\n    30\t    }\n    31\t    .card-body {\n    32\t      padding: 20px;\n    33\t    }\n    34\t    .btn {\n    35\t      margin-right: 10px;\n    36\t    }\n    37\t    .form-label {\n    38\t      font-weight: 500;\n    39\t    }\n    40\t    .alert {\n    41\t      padding: 10px;\n    42\t      border-radius: 4px;\n    43\t      margin-top: 10px;\n    44\t    }\n    45\t    .alert-info {\n    46\t      background-color: #d1ecf1;\n    47\t      border-color: #bee5eb;\n    48\t      color: #0c5460;\n    49\t    }\n    50\t    .alert-success {\nTotal lines in file: 300\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01J2HZh1XZDsDCmjJxeE6GEh&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:30.382Z&quot;,&quot;response_text&quot;:&quot;让我查看主要的JavaScript文件：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;6c9063c4-3103-46f3-a15a-03948b410032&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C5NdBf9CYBjCzDznUSGPTo&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;uploads\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看uploads目录：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on public/js/main.js:\n     1\t// 全局变量\n     2\tlet currentFilePath = '';\n     3\tlet currentSheetName = '';\n     4\tlet excelData = null;\n     5\tlet searchResults = null;\n     6\tlet splitResults = null;\n     7\tlet dedupResults = null;\n     8\t\n     9\t// 防止 MutationObserver 错误\n    10\tdocument.addEventListener('DOMContentLoaded', function() {\n    11\t  // 捕获可能的 MutationObserver 错误\n    12\t  window.addEventListener('error', function(event) {\n    13\t    if (event.error &amp;&amp; event.error.message &amp;&amp; event.error.message.includes('MutationObserver')) {\n    14\t      console.warn('捕获到 MutationObserver 错误:', event.error.message);\n    15\t      event.preventDefault();\n    16\t      return true;\n    17\t    }\n    18\t  });\n    19\t});\n    20\t\n    21\t// 在 main.js 文件开头添加\n    22\t// 额外的错误处理\n    23\tconsole.log('main.js 加载中...');\n    24\t\n    25\t// 全局错误处理函数\n    26\twindow.onerror = function(message, source, lineno, colno, error) {\n    27\t  console.warn('全局错误:', message, 'at', source, lineno, colno);\n    28\t  // 返回 true 表示错误已处理\n    29\t  if (message &amp;&amp; (message.includes('MutationObserver') || message.includes('observe'))) {\n    30\t    return true;\n    31\t  }\n    32\t  return false;\n    33\t};\n    34\t\n    35\t// 其余 main.js 代码保持不变\n    36\t\n    37\t// DOM 元素\n    38\tdocument.addEventListener('DOMContentLoaded', function() {\n    39\t  // 初始化日期选择器\n    40\t  initializeDateSelectors();\n    41\t  \n    42\t  // 初始化事件监听器\n    43\t  initializeEventListeners();\n    44\t});\n    45\t\n    46\t// 初始化日期选择器\n    47\tfunction initializeDateSelectors() {\n    48\t  const yearSelectors = ['dedupYear', 'splitYear'];\n    49\t  const monthSelectors = ['dedupMonth', 'splitMonth'];\n    50\t  \n    51\t  // 初始化年份选择器\n    52\t  yearSelectors.forEach(id =&gt; {\n    53\t    const selector = document.getElementById(id);\n    54\t    if (selector) {\n    55\t      // 添加空选项\n    56\t      const emptyOption = document.createElement('option');\n    57\t      emptyOption.value = '';\n    58\t      emptyOption.textContent = '-- 选择年份 --';\n    59\t      selector.appendChild(emptyOption);\n    60\t      \n    61\t      // 添加年份选项（当前年份前后5年）\n    62\t      const currentYear = new Date().getFullYear();\n    63\t      for (let year = currentYear - 10; year &lt;= currentYear + 15; year++) {\n    64\t        const option = document.createElement('option');\n    65\t        option.value = year;\n    66\t        option.textContent = year;\n    67\t        selector.appendChild(option);\n    68\t      }\n    69\t    }\n    70\t  });\n    71\t  \n    72\t  // 初始化月份选择器\n    73\t  monthSelectors.forEach(id =&gt; {\n    74\t    const selector = document.getElementById(id);\n    75\t    if (selector) {\n    76\t      // 添加空选项\n    77\t      const emptyOption = document.createElement('option');\n    78\t      emptyOption.value = '';\n    79\t      emptyOption.textContent = '-- 选择月份 --';\n    80\t      selector.appendChild(emptyOption);\n    81\t      \n    82\t      // 添加月份选项\n    83\t      for (let month = 1; month &lt;= 12; month++) {\n    84\t        const option = document.createElement('option');\n    85\t        option.value = month;\n    86\t        option.textContent = month;\n    87\t        selector.appendChild(option);\n    88\t      }\n    89\t    }\n    90\t  });\n    91\t  \n    92\t  // 初始化日期筛选选项显示/隐藏\n    93\t  const dateFilterCheckboxes = [\n    94\t    { checkbox: 'dedupEnableDateFilter', options: 'dedupDateFilterOptions' },\n    95\t    { checkbox: 'splitEnableDateFilter', options: 'splitDateFilterOptions' }\n    96\t  ];\n    97\t  \n    98\t  dateFilterCheckboxes.forEach(item =&gt; {\n    99\t    const checkbox = document.getElementById(item.checkbox);\n   100\t    const options = document.getElementById(item.options);\nTotal lines in file: 969\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01Cbnk5gj8qMh6JuqqQHuCDj&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:34.395Z&quot;,&quot;response_text&quot;:&quot;现在让我查看uploads目录：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;088fe792-41aa-404a-99e5-1a31c14826ed&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01R64LE1dSg7RoQFjz3AX7ZU&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;public/index.html\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [100, 200]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对项目的分析，这是一个 **Excel 电话号码处理工具** 的 Web 应用项目。让我为您总结项目的主要特点：\n\n## 项目概述\n\n这是一个基于 Node.js + Express 的 Web 应用，专门用于处理 Excel 文件中的电话号码数据。\n\n### 技术栈\n- **后端**: Node.js + Express\n- **前端**: HTML + JavaScript + Bootstrap\n- **核心依赖**:\n  - `xlsx`: 处理 Excel 文件读写\n  - `multer`: 处理文件上传\n  - `express`: Web 服务器框架\n  - `cors`: 跨域支持\n  - `body-parser`: 请求体解析\n\n### 主要功能\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in uploads, excluding hidden items:\n(empty directory)\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C5NdBf9CYBjCzDznUSGPTo&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:38.715Z&quot;,&quot;response_text&quot;:&quot;基于我对项目的分析，这是一个 **Excel 电话号码处理工具** 的 Web 应用项目。让我为您总结项目的主要特点：\n\n## 项目概述\n\n这是一个基于 Node.js + Express 的 Web 应用，专门用于处理 Excel 文件中的电话号码数据。\n\n### 技术栈\n- **后端**: Node.js + Express\n- **前端**: HTML + JavaScript + Bootstrap\n- **核心依赖**:\n  - `xlsx`: 处理 Excel 文件读写\n  - `multer`: 处理文件上传\n  - `express`: Web 服务器框架\n  - `cors`: 跨域支持\n  - `body-parser`: 请求体解析\n\n### 主要功能\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c4326f87-3ea6-4b6a-86f9-3a90fc5783ae&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:0,&quot;content&quot;:&quot;基于完整的项目分析，这是一个功能完整的 **Excel 电话号码处理工具**。以下是详细的项目总结：\n\n##  项目功能\n\n### 1. **电话号码去重** (`/api/dedup-phones`)\n- 上传 Excel 文件，选择工作表和电话号码列\n- 支持移除国家代码 (+86/86)\n- 可选择保持原始格式\n- 支持按日期筛选数据\n- 返回去重后的唯一号码和重复号码\n\n### 2. **关键词搜索** (`/api/search-excel`)\n- 在指定列中搜索关键词\n- 支持大小写敏感/不敏感搜索\n- 支持精确匹配或模糊匹配\n- 可对搜索结果进行电话号码去重\n\n### 3. **多电话号码分离** (`/api/split-phones`)\n- 将一个单元格中的多个电话号码分离成多行\n- 支持多种分隔符：`,` `;` `，` `；` 空格 `/` `|` 换行符\n- 智能识别连续的11位数字组合\n- 支持去重和日期筛选\n\n### 4. **Excel 导出** (`/api/export-excel`)\n- 将处理结果导出为新的 Excel 文件\n- 支持自定义工作表名称\n- 限制最大导出10万行数据\n\n## ️ 技术架构\n\n### 后端 (server.js)\n&lt;augment_code_snippet path=\&quot;server.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nconst express = require('express');\nconst multer = require('multer');\nconst XLSX = require('xlsx');\nconst path = require('path');\nconst fs = require('fs');\n```\n&lt;/augment_code_snippet&gt;\n\n### 前端特点\n- 响应式设计，使用 Bootstrap 框架\n- 标签页界面，功能分类清晰\n- 实时状态反馈和进度显示\n- 错误处理和用户友好提示\n\n##  核心功能实现\n\n### 电话号码清理算法\n&lt;augment_code_snippet path=\&quot;server.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\nfunction cleanPhoneNumber(phone, removeCountryCode = false) {\n  if (!phone) return '';\n  \n  // 转换为字符串\n  let phoneStr = String(phone);\n  \n  // 移除所有非数字字符\n  phoneStr = phoneStr.replace(/\\D/g, '');\n  \n  // 如果需要移除国家代码（假设是+86或86开头）\n  if (removeCountryCode &amp;&amp; phoneStr.length &gt; 10) {\n    if (phoneStr.startsWith('86') &amp;&amp; phoneStr.length &gt; 11) {\n      phoneStr = phoneStr.substring(2);\n    }\n  }\n  \n  return phoneStr;\n}\n```\n&lt;/augment_code_snippet&gt;\n\n### 性能优化\n- **分批处理**: 每批处理5000行数据，避免内存溢出\n- **流式文件传输**: 使用文件流进行大文件下载\n- **结果数量限制**: 限制搜索和分离结果数量\n- **优化的Excel读取选项**: 禁用不必要的功能提高性能\n\n##  项目结构\n```\nexcel-web-node/\n├── server.js              # 主服务器文件\n├── package.json           # 项目配置和依赖\n├── public/                # 前端静态文件\n│   ├── index.html         # 主页面\n│   ├── css/               # 样式文件\n│   └── js/                # JavaScript文件\n└── uploads/               # 文件上传目录\n```\n\n##  启动方式\n项目监听3000端口，支持本地和局域网访问：\n\n&lt;augment_code_snippet path=\&quot;server.js\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```javascript\napp.listen(port, () =&gt; {\n  const localIP = getLocalIP();\n  console.log(`服务器已启动:`);\n  console.log(`- 本地访问: http://localhost:${port}`);\n  console.log(`- 局域网访问: http://${localIP}:${port}`);\n});\n```\n&lt;/augment_code_snippet&gt;\n\n这是一个专业的数据处理工具，特别适合需要批量处理Excel中电话号码数据的场景，如客户数据清理、营销名单整理等。&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;基于完整的项目分析，这是一个功能完整的 **Excel 电话号码处理工具**。以下是详细的项目总结：\n\n##  项目功能\n\n### 1. **电话号码去重** (`/api/dedup-phones`)\n- 上传 Excel 文件，选择工作表和电话号码列\n- 支持移除国家代码 (+86/86)\n- 可选择保持原始格式\n- 支持按日期筛选数据\n- 返回去重后的唯一号码和重复号码\n\n### 2. **关键词搜索** (`/api/search-excel`)\n- 在指定列中搜索关键词\n- 支持大小写敏感/不敏感搜索\n- 支持精确匹配或模糊匹配\n- 可对搜索结果进行电话号码去重\n\n### 3. **多电话号码分离** (`/api/split-phones`)\n- 将一个单元格中的多个电话号码分离成多行\n- 支持多种分隔符：`,` `;` `，` `；` 空格 `/` `|` 换行符\n- 智能识别连续的11位数字组合\n- 支持去重和日期筛选\n\n### 4. **Excel 导出** (`/api/export-excel`)\n- 将处理结果导出为新的 Excel 文件\n- 支持自定义工作表名称\n- 限制最大导出10万行数据\n\n## ️ 技术架构\n\n### 后端 (server.js)\n````javascript path=server.js mode=EXCERPT\nconst express = require('express');\nconst multer = require('multer');\nconst XLSX = require('xlsx');\nconst path = require('path');\nconst fs = require('fs');\n````\n\n### 前端特点\n- 响应式设计，使用 Bootstrap 框架\n- 标签页界面，功能分类清晰\n- 实时状态反馈和进度显示\n- 错误处理和用户友好提示\n\n##  核心功能实现\n\n### 电话号码清理算法\n````javascript path=server.js mode=EXCERPT\nfunction cleanPhoneNumber(phone, removeCountryCode = false) {\n  if (!phone) return '';\n  \n  // 转换为字符串\n  let phoneStr = String(phone);\n  \n  // 移除所有非数字字符\n  phoneStr = phoneStr.replace(/\\D/g, '');\n  \n  // 如果需要移除国家代码（假设是+86或86开头）\n  if (removeCountryCode &amp;&amp; phoneStr.length &gt; 10) {\n    if (phoneStr.startsWith('86') &amp;&amp; phoneStr.length &gt; 11) {\n      phoneStr = phoneStr.substring(2);\n    }\n  }\n  \n  return phoneStr;\n}\n````\n\n### 性能优化\n- **分批处理**: 每批处理5000行数据，避免内存溢出\n- **流式文件传输**: 使用文件流进行大文件下载\n- **结果数量限制**: 限制搜索和分离结果数量\n- **优化的Excel读取选项**: 禁用不必要的功能提高性能\n\n##  项目结构\n```\nexcel-web-node/\n├── server.js              # 主服务器文件\n├── package.json           # 项目配置和依赖\n├── public/                # 前端静态文件\n│   ├── index.html         # 主页面\n│   ├── css/               # 样式文件\n│   └── js/                # JavaScript文件\n└── uploads/               # 文件上传目录\n```\n\n##  启动方式\n项目监听3000端口，支持本地和局域网访问：\n\n````javascript path=server.js mode=EXCERPT\napp.listen(port, () =&gt; {\n  const localIP = getLocalIP();\n  console.log(`服务器已启动:`);\n  console.log(`- 本地访问: http://localhost:${port}`);\n  console.log(`- 局域网访问: http://${localIP}:${port}`);\n});\n````\n\n这是一个专业的数据处理工具，特别适合需要批量处理Excel中电话号码数据的场景，如客户数据清理、营销名单整理等。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on public/index.html:\n   100\t              &lt;/div&gt;\n   101\t              \n   102\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   103\t                &lt;label for=\&quot;dedupSheet\&quot; class=\&quot;form-label\&quot;&gt;选择工作表&lt;/label&gt;\n   104\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupSheet\&quot; disabled&gt;&lt;/select&gt;\n   105\t              &lt;/div&gt;\n   106\t              \n   107\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   108\t                &lt;label for=\&quot;dedupPhoneColumn\&quot; class=\&quot;form-label\&quot;&gt;选择电话号码列&lt;/label&gt;\n   109\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupPhoneColumn\&quot; disabled&gt;&lt;/select&gt;\n   110\t              &lt;/div&gt;\n   111\t              \n   112\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   113\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupRemoveCountryCode\&quot;&gt;\n   114\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupRemoveCountryCode\&quot;&gt;移除国家代码 (+86/86)&lt;/label&gt;\n   115\t              &lt;/div&gt;\n   116\t              \n   117\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   118\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupKeepOriginalFormat\&quot;&gt;\n   119\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupKeepOriginalFormat\&quot;&gt;保持原始格式&lt;/label&gt;\n   120\t              &lt;/div&gt;\n   121\t              \n   122\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   123\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupEnableDateFilter\&quot;&gt;\n   124\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupEnableDateFilter\&quot;&gt;启用日期筛选&lt;/label&gt;\n   125\t              &lt;/div&gt;\n   126\t              \n   127\t              &lt;div id=\&quot;dedupDateFilterOptions\&quot; style=\&quot;display: none;\&quot;&gt;\n   128\t                &lt;div class=\&quot;mb-3\&quot;&gt;\n   129\t                  &lt;label for=\&quot;dedupDateColumn\&quot; class=\&quot;form-label\&quot;&gt;选择日期列&lt;/label&gt;\n   130\t                  &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupDateColumn\&quot;&gt;&lt;/select&gt;\n   131\t                &lt;/div&gt;\n   132\t                \n   133\t                &lt;div class=\&quot;row\&quot;&gt;\n   134\t                  &lt;div class=\&quot;col-md-6 mb-3\&quot;&gt;\n   135\t                    &lt;label for=\&quot;dedupYear\&quot; class=\&quot;form-label\&quot;&gt;年份&lt;/label&gt;\n   136\t                    &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupYear\&quot;&gt;&lt;/select&gt;\n   137\t                  &lt;/div&gt;\n   138\t                  \n   139\t                  &lt;div class=\&quot;col-md-6 mb-3\&quot;&gt;\n   140\t                    &lt;label for=\&quot;dedupMonth\&quot; class=\&quot;form-label\&quot;&gt;月份&lt;/label&gt;\n   141\t                    &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupMonth\&quot;&gt;&lt;/select&gt;\n   142\t                  &lt;/div&gt;\n   143\t                &lt;/div&gt;\n   144\t              &lt;/div&gt;\n   145\t              \n   146\t              &lt;button type=\&quot;submit\&quot; class=\&quot;btn btn-primary\&quot; id=\&quot;dedupButton\&quot; disabled&gt;去重处理&lt;/button&gt;\n   147\t              &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-success\&quot; id=\&quot;dedupExportButton\&quot; disabled&gt;导出结果&lt;/button&gt;\n   148\t            &lt;/form&gt;\n   149\t            \n   150\t            &lt;div class=\&quot;mt-3\&quot; id=\&quot;dedupResult\&quot;&gt;&lt;/div&gt;\n   151\t            &lt;div class=\&quot;mt-3\&quot; id=\&quot;dedupStatus\&quot;&gt;&lt;/div&gt;\n   152\t          &lt;/div&gt;\n   153\t        &lt;/div&gt;\n   154\t      &lt;/div&gt;\n   155\t      \n   156\t      &lt;!-- 关键词搜索标签页 --&gt;\n   157\t      &lt;div class=\&quot;tab-pane fade\&quot; id=\&quot;search\&quot; role=\&quot;tabpanel\&quot; aria-labelledby=\&quot;search-tab\&quot;&gt;\n   158\t        &lt;div class=\&quot;card mt-3\&quot;&gt;\n   159\t          &lt;div class=\&quot;card-body\&quot;&gt;\n   160\t            &lt;!-- &lt;h5 class=\&quot;card-title\&quot;&gt;关键词搜索&lt;/h5&gt; --&gt;\n   161\t            \n   162\t            &lt;form id=\&quot;searchForm\&quot;&gt;\n   163\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   164\t                &lt;label for=\&quot;searchFile\&quot; class=\&quot;form-label\&quot;&gt;选择 Excel 文件&lt;/label&gt;\n   165\t                &lt;input type=\&quot;file\&quot; class=\&quot;form-control\&quot; id=\&quot;searchFile\&quot; accept=\&quot;.xlsx,.xls\&quot; required&gt;\n   166\t              &lt;/div&gt;\n   167\t              \n   168\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   169\t                &lt;label for=\&quot;searchSheet\&quot; class=\&quot;form-label\&quot;&gt;选择工作表&lt;/label&gt;\n   170\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;searchSheet\&quot; disabled&gt;&lt;/select&gt;\n   171\t              &lt;/div&gt;\n   172\t              \n   173\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   174\t                &lt;label for=\&quot;searchColumn\&quot; class=\&quot;form-label\&quot;&gt;选择搜索列&lt;/label&gt;\n   175\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;searchColumn\&quot; disabled&gt;&lt;/select&gt;\n   176\t              &lt;/div&gt;\n   177\t              \n   178\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   179\t                &lt;label for=\&quot;searchKeyword\&quot; class=\&quot;form-label\&quot;&gt;搜索关键词&lt;/label&gt;\n   180\t                &lt;input type=\&quot;text\&quot; class=\&quot;form-control\&quot; id=\&quot;searchKeyword\&quot; required&gt;\n   181\t              &lt;/div&gt;\n   182\t              \n   183\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   184\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchCaseSensitive\&quot;&gt;\n   185\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchCaseSensitive\&quot;&gt;区分大小写&lt;/label&gt;\n   186\t              &lt;/div&gt;\n   187\t              \n   188\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   189\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchExactMatch\&quot;&gt;\n   190\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchExactMatch\&quot;&gt;精确匹配&lt;/label&gt;\n   191\t              &lt;/div&gt;\n   192\t              \n   193\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   194\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchDeduplicateResults\&quot;&gt;\n   195\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchDeduplicateResults\&quot;&gt;对结果去重&lt;/label&gt;\n   196\t              &lt;/div&gt;\n   197\t              \n   198\t              &lt;button type=\&quot;submit\&quot; class=\&quot;btn btn-primary\&quot; id=\&quot;searchButton\&quot; disabled&gt;搜索&lt;/button&gt;\n   199\t              &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-success\&quot; id=\&quot;searchExportButton\&quot; disabled&gt;导出结果&lt;/button&gt;\n   200\t            &lt;/form&gt;\nTotal lines in file: 300\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01R64LE1dSg7RoQFjz3AX7ZU&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/Documents/excel-web-node&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-05T12:23:46.376Z&quot;,&quot;response_text&quot;:&quot;基于完整的项目分析，这是一个功能完整的 **Excel 电话号码处理工具**。以下是详细的项目总结：\n\n##  项目功能\n\n### 1. **电话号码去重** (`/api/dedup-phones`)\n- 上传 Excel 文件，选择工作表和电话号码列\n- 支持移除国家代码 (+86/86)\n- 可选择保持原始格式\n- 支持按日期筛选数据\n- 返回去重后的唯一号码和重复号码\n\n### 2. **关键词搜索** (`/api/search-excel`)\n- 在指定列中搜索关键词\n- 支持大小写敏感/不敏感搜索\n- 支持精确匹配或模糊匹配\n- 可对搜索结果进行电话号码去重\n\n### 3. **多电话号码分离** (`/api/split-phones`)\n- 将一个单元格中的多个电话号码分离成多行\n- 支持多种分隔符：`,` `;` `，` `；` 空格 `/` `|` 换行符\n- 智能识别连续的11位数字组合\n- 支持去重和日期筛选\n\n### 4. **Excel 导出** (`/api/export-excel`)\n- 将处理结果导出为新的 Excel 文件\n- 支持自定义工作表名称\n- 限制最大导出10万行数据\n\n## ️ 技术架构\n\n### 后端 (server.js)\n````javascript path=server.js mode=EXCERPT\nconst express = require('express');\nconst multer = require('multer');\nconst XLSX = require('xlsx');\nconst path = require('path');\nconst fs = require('fs');\n````\n\n### 前端特点\n- 响应式设计，使用 Bootstrap 框架\n- 标签页界面，功能分类清晰\n- 实时状态反馈和进度显示\n- 错误处理和用户友好提示\n\n##  核心功能实现\n\n### 电话号码清理算法\n````javascript path=server.js mode=EXCERPT\nfunction cleanPhoneNumber(phone, removeCountryCode = false) {\n  if (!phone) return '';\n  \n  // 转换为字符串\n  let phoneStr = String(phone);\n  \n  // 移除所有非数字字符\n  phoneStr = phoneStr.replace(/\\D/g, '');\n  \n  // 如果需要移除国家代码（假设是+86或86开头）\n  if (removeCountryCode &amp;&amp; phoneStr.length &gt; 10) {\n    if (phoneStr.startsWith('86') &amp;&amp; phoneStr.length &gt; 11) {\n      phoneStr = phoneStr.substring(2);\n    }\n  }\n  \n  return phoneStr;\n}\n````\n\n### 性能优化\n- **分批处理**: 每批处理5000行数据，避免内存溢出\n- **流式文件传输**: 使用文件流进行大文件下载\n- **结果数量限制**: 限制搜索和分离结果数量\n- **优化的Excel读取选项**: 禁用不必要的功能提高性能\n\n##  项目结构\n```\nexcel-web-node/\n├── server.js              # 主服务器文件\n├── package.json           # 项目配置和依赖\n├── public/                # 前端静态文件\n│   ├── index.html         # 主页面\n│   ├── css/               # 样式文件\n│   └── js/                # JavaScript文件\n└── uploads/               # 文件上传目录\n```\n\n##  启动方式\n项目监听3000端口，支持本地和局域网访问：\n\n````javascript path=server.js mode=EXCERPT\napp.listen(port, () =&gt; {\n  const localIP = getLocalIP();\n  console.log(`服务器已启动:`);\n  console.log(`- 本地访问: http://localhost:${port}`);\n  console.log(`- 局域网访问: http://${localIP}:${port}`);\n});\n````\n\n这是一个专业的数据处理工具，特别适合需要批量处理Excel中电话号码数据的场景，如客户数据清理、营销名单整理等。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-92036596-7902-4efd-b42f-cf71b3aaa487&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3d1b5f3e-312a-44c7-9706-2e3af736a2f6&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-898780b0-b843-478a-ae07-de099eab139b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-872633e8-b9ec-4f47-b02c-f8f0594c9f28&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-1547acb7-2ac2-4a99-83cb-1438d1a10a75&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5d71b8ee-acab-467f-afe5-c9c895a51db0&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e2f396da-773b-4089-98b7-164ebfcde1e1&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-771dcdb2-6d6b-42ef-ac5b-d626baf27db2&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-abadb7e9-0a58-49ce-84e5-964086ebb9fc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-54567796-4fb7-4b15-89a5-8d5206f1173a&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;4e9cf675-5e05-4cfd-9a83-66d093d6cf25;toolu_vrtx_013YkYigCocAD82QjXS8hDzV&quot;:{&quot;requestId&quot;:&quot;4e9cf675-5e05-4cfd-9a83-66d093d6cf25&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_013YkYigCocAD82QjXS8hDzV&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./package-lock.json\n./package.json\n./public\n./public/css\n./public/index.html\n./public/js\n./server.js\n./uploads\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;bdc0ef9c-7c3f-481a-bd87-468dfa2d93c8;toolu_vrtx_013DhAu8Z4eEuurHZmGeLgvd&quot;:{&quot;requestId&quot;:&quot;bdc0ef9c-7c3f-481a-bd87-468dfa2d93c8&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_013DhAu8Z4eEuurHZmGeLgvd&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on package.json:\n     1\t{\n     2\t  \&quot;name\&quot;: \&quot;excel-web-node\&quot;,\n     3\t  \&quot;version\&quot;: \&quot;1.0.0\&quot;,\n     4\t  \&quot;main\&quot;: \&quot;index.js\&quot;,\n     5\t  \&quot;scripts\&quot;: {\n     6\t    \&quot;test\&quot;: \&quot;echo \\\&quot;Error: no test specified\\\&quot; &amp;&amp; exit 1\&quot;\n     7\t  },\n     8\t  \&quot;keywords\&quot;: [],\n     9\t  \&quot;author\&quot;: \&quot;\&quot;,\n    10\t  \&quot;license\&quot;: \&quot;ISC\&quot;,\n    11\t  \&quot;description\&quot;: \&quot;\&quot;,\n    12\t  \&quot;dependencies\&quot;: {\n    13\t    \&quot;body-parser\&quot;: \&quot;^1.20.3\&quot;,\n    14\t    \&quot;cors\&quot;: \&quot;^2.8.5\&quot;,\n    15\t    \&quot;express\&quot;: \&quot;^4.21.2\&quot;,\n    16\t    \&quot;multer\&quot;: \&quot;^1.4.5-lts.1\&quot;,\n    17\t    \&quot;xlsx\&quot;: \&quot;^0.18.5\&quot;\n    18\t  }\n    19\t}\n    20\t\nTotal lines in file: 20\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;8423232c-db86-4d33-a2d8-06817ad28f8d;toolu_vrtx_01HJZmjKih5viNCAaZ9fxh7z&quot;:{&quot;requestId&quot;:&quot;8423232c-db86-4d33-a2d8-06817ad28f8d&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01HJZmjKih5viNCAaZ9fxh7z&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on server.js:\n     1\tconst express = require('express');\n     2\tconst multer = require('multer');\n     3\tconst XLSX = require('xlsx');\n     4\tconst path = require('path');\n     5\tconst fs = require('fs');\n     6\tconst os = require('os');\n     7\t\n     8\t// 创建 Express 应用\n     9\tconst app = express();\n    10\tconst port = 3000;\n    11\t\n    12\t// 配置中间件 - 增加请求体大小限制\n    13\tapp.use(express.json({ limit: '100mb' }));\n    14\tapp.use(express.urlencoded({ extended: true, limit: '100mb' }));\n    15\t\n    16\t// 添加超时设置\n    17\tapp.use((req, res, next) =&gt; {\n    18\t  // 设置较长的超时时间，避免大文件处理时请求超时\n    19\t  req.setTimeout(300000); // 5分钟\n    20\t  res.setTimeout(300000); // 5分钟\n    21\t  next();\n    22\t});\n    23\t\n    24\t// 静态文件服务\n    25\tapp.use(express.static(path.join(__dirname, 'public')));\n    26\t\n    27\t// 确保上传目录存在\n    28\tconst uploadsDir = path.join(__dirname, 'uploads');\n    29\tif (!fs.existsSync(uploadsDir)) {\n    30\t  fs.mkdirSync(uploadsDir);\n    31\t}\n    32\t\n    33\t// 配置文件上传\n    34\tconst storage = multer.diskStorage({\n    35\t  destination: function (req, file, cb) {\n    36\t    cb(null, uploadsDir);\n    37\t  },\n    38\t  filename: function (req, file, cb) {\n    39\t    cb(null, Date.now() + '-' + file.originalname);\n    40\t  }\n    41\t});\n    42\t\n    43\t// 设置 multer 配置，增加文件大小限制\n    44\tconst upload = multer({ \n    45\t  storage: storage,\n    46\t  limits: {\n    47\t    fileSize: 100 * 1024 * 1024 // 增加到100MB 限制\n    48\t  }\n    49\t});\n    50\t\n    51\t// 清理电话号码函数\n    52\tfunction cleanPhoneNumber(phone, removeCountryCode = false) {\n    53\t  if (!phone) return '';\n    54\t  \n    55\t  // 转换为字符串\n    56\t  let phoneStr = String(phone);\n    57\t  \n    58\t  // 移除所有非数字字符\n    59\t  phoneStr = phoneStr.replace(/\\D/g, '');\n    60\t  \n    61\t  // 如果需要移除国家代码（假设是+86或86开头）\n    62\t  if (removeCountryCode &amp;&amp; phoneStr.length &gt; 10) {\n    63\t    if (phoneStr.startsWith('86') &amp;&amp; phoneStr.length &gt; 11) {\n    64\t      phoneStr = phoneStr.substring(2);\n    65\t    } else if (phoneStr.length &gt; 11) {\n    66\t      // 如果长度大于11，假设前面的是国家代码\n    67\t      phoneStr = phoneStr.substring(phoneStr.length - 11);\n    68\t    }\n    69\t  }\n    70\t  \n    71\t  return phoneStr;\n    72\t}\n    73\t\n    74\t// 日期解析函数\n    75\tfunction parseDate(dateStr) {\n    76\t  if (!dateStr) return null;\n    77\t  \n    78\t  // 转换为字符串以确保一致性\n    79\t  const str = String(dateStr).trim();\n    80\t  \n    81\t  // 尝试直接解析\n    82\t  let date = new Date(str);\n    83\t  if (!isNaN(date.getTime())) {\n    84\t    return date;\n    85\t  }\n    86\t  \n    87\t  // 处理YYYY/MM/DD或YYYY-MM-DD格式\n    88\t  let match = str.match(/^(\\d{4})[\\/\\-\\.](\\d{1,2})[\\/\\-\\.](\\d{1,2})$/);\n    89\t  if (match) {\n    90\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n    91\t    return date;\n    92\t  }\n    93\t  \n    94\t  // 处理DD/MM/YYYY或DD-MM-YYYY格式\n    95\t  match = str.match(/^(\\d{1,2})[\\/\\-\\.](\\d{1,2})[\\/\\-\\.](\\d{4})$/);\n    96\t  if (match) {\n    97\t    date = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));\n    98\t    return date;\n    99\t  }\n   100\t  \n   101\t  // 处理YYYY年MM月DD日格式\n   102\t  match = str.match(/^(\\d{4})年(\\d{1,2})月(\\d{1,2})日?$/);\n   103\t  if (match) {\n   104\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n   105\t    return date;\n   106\t  }\n   107\t  \n   108\t  // 处理YYYYMMDD格式\n   109\t  match = str.match(/^(\\d{4})(\\d{2})(\\d{2})$/);\n   110\t  if (match) {\n   111\t    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));\n   112\t    return date;\n   113\t  }\n   114\t  \n   115\t  return null;\n   116\t}\n   117\t\n   118\t// 获取本机 IP 地址\n   119\tfunction getLocalIP() {\n   120\t  const nets = os.networkInterfaces();\n   121\t  for (const name of Object.keys(nets)) {\n   122\t    for (const net of nets[name]) {\n   123\t      if (net.family === 'IPv4' &amp;&amp; !net.internal) {\n   124\t        return net.address;\n   125\t      }\n   126\t    }\n   127\t  }\n   128\t  return '127.0.0.1';\n   129\t}\n   130\t// API 路由: 获取 Excel 文件的工作表列表\n   131\tapp.post('/api/get-sheets', upload.single('file'), (req, res) =&gt; {\n   132\t  console.log('收到获取工作表请求');\n   133\t  try {\n   134\t    if (!req.file) {\n   135\t      console.log('未上传文件');\n   136\t      return res.status(400).json({ error: '未上传文件' });\n   137\t    }\n   138\t    \n   139\t    console.log('文件路径:', req.file.path);\n   140\t    const filePath = req.file.path;\n   141\t    \n   142\t    // 检查文件是否存在\n   143\t    if (!fs.existsSync(filePath)) {\n   144\t      console.log('文件不存在:', filePath);\n   145\t      return res.status(400).json({ error: '文件不存在' });\n   146\t    }\n   147\t    \n   148\t    // 检查文件大小\n   149\t    const stats = fs.statSync(filePath);\n   150\t    console.log('文件大小:', stats.size, '字节');\n   151\t    \n   152\t    // 读取Excel文件 - 优化读取选项\n   153\t    const workbook = XLSX.readFile(filePath, {\n   154\t      cellFormula: false,\n   155\t      cellStyles: false,\n   156\t      cellDates: true,\n   157\t      dense: true,\n   158\t      sheetStubs: true,\n   159\t      type: 'binary',\n   160\t      rawHTML: false,\n   161\t      cellHTML: false,\n   162\t      sheetRows: 1 // 只读取第一行以获取表头，提高性能\n   163\t    });\n   164\t    \n   165\t    const sheetNames = workbook.SheetNames;\n   166\t    console.log('工作表名称:', sheetNames);\n   167\t    \n   168\t    const result = {\n   169\t      filePath: filePath,\n   170\t      sheetNames: sheetNames,\n   171\t      sheets: {}\n   172\t    };\n   173\t    \n   174\t    // 获取每个工作表的列头信息\n   175\t    sheetNames.forEach(sheetName =&gt; {\n   176\t      const worksheet = workbook.Sheets[sheetName];\n   177\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   178\t        header: 1, \n   179\t        defval: '',\n   180\t        range: 0,\n   181\t        blankrows: false,\n   182\t        sheetRows: 1 // 只读取第一行以获取表头\n   183\t      });\n   184\t      \n   185\t      if (jsonData.length &gt; 0) {\n   186\t        result.sheets[sheetName] = {\n   187\t          headers: jsonData[0]\n   188\t        };\n   189\t      } else {\n   190\t        result.sheets[sheetName] = {\n   191\t          headers: []\n   192\t        };\n   193\t      }\n   194\t    });\n   195\t    \n   196\t    console.log('成功获取工作表信息');\n   197\t    res.json(result);\n   198\t  } catch (error) {\n   199\t    console.error('处理Excel文件出错:', error);\n   200\t    res.status(500).json({ error: error.message });\n   201\t  }\n   202\t});\n   203\t\n   204\t// API 路由: 电话号码去重\n   205\tapp.post('/api/dedup-phones', (req, res) =&gt; {\n   206\t  console.log('收到去重请求:', req.body);\n   207\t  try {\n   208\t    const { filePath, sheetName, phoneColumnIndex, removeCountryCode, keepOriginalFormat, dateFilter } = req.body;\n   209\t    \n   210\t    if (!filePath || !sheetName) {\n   211\t      return res.status(400).json({ error: '缺少必要参数' });\n   212\t    }\n   213\t    \n   214\t    // 检查文件是否存在\n   215\t    if (!fs.existsSync(filePath)) {\n   216\t      return res.status(400).json({ error: '文件不存在' });\n   217\t    }\n   218\t    \n   219\t    // 读取Excel文件 - 使用优化的读取选项\n   220\t    const options = {\n   221\t      cellFormula: false,\n   222\t      cellStyles: false,\n   223\t      cellDates: true,\n   224\t      dense: true,\n   225\t      sheetStubs: true,\n   226\t      type: 'binary',\n   227\t      rawHTML: false,\n   228\t      cellHTML: false\n   229\t    };\n   230\t    \n   231\t    const workbook = XLSX.readFile(filePath, options);\n   232\t    \n   233\t    if (!workbook.SheetNames.includes(sheetName)) {\n   234\t      return res.status(400).json({ error: '工作表不存在' });\n   235\t    }\n   236\t    \n   237\t    // 获取工作表\n   238\t    const worksheet = workbook.Sheets[sheetName];\n   239\t    \n   240\t    // 获取表头\n   241\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   242\t      header: 1, \n   243\t      defval: '',\n   244\t      blankrows: false,\n   245\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   246\t    })[0] || [];\n   247\t    \n   248\t    // 分批处理数据\n   249\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   250\t    const uniquePhones = new Map();\n   251\t    const duplicates = new Map();\n   252\t    let totalProcessed = 0;\n   253\t    \n   254\t    // 获取工作表范围\n   255\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   256\t    const totalRows = range.e.r;\n   257\t    \n   258\t    // 分批处理数据\n   259\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   260\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   261\t      \n   262\t      // 读取当前批次的数据\n   263\t      const batchRange = {\n   264\t        s: { r: startRow, c: 0 },\n   265\t        e: { r: endRow, c: range.e.c }\n   266\t      };\n   267\t      \n   268\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   269\t        header: 1, \n   270\t        defval: '',\n   271\t        blankrows: false,\n   272\t        range: batchRange\n   273\t      });\n   274\t      \n   275\t      // 应用日期筛选\n   276\t      let filteredData = jsonData;\n   277\t      if (dateFilter &amp;&amp; dateFilter.enabled &amp;&amp; dateFilter.columnIndex &gt;= 0) {\n   278\t        filteredData = jsonData.filter(row =&gt; {\n   279\t          if (dateFilter.columnIndex &gt;= row.length || !row[dateFilter.columnIndex]) {\n   280\t            return false;\n   281\t          }\n   282\t          \n   283\t          const date = parseDate(row[dateFilter.columnIndex]);\n   284\t          if (!date) return false;\n   285\t          \n   286\t          const year = date.getFullYear();\n   287\t          const month = date.getMonth() + 1;\n   288\t          \n   289\t          let match = true;\n   290\t          if (dateFilter.year &amp;&amp; dateFilter.year !== '') {\n   291\t            const filterYear = parseInt(dateFilter.year);\n   292\t            if (year !== filterYear) {\n   293\t              match = false;\n   294\t            }\n   295\t          }\n   296\t          \n   297\t          if (match &amp;&amp; dateFilter.month &amp;&amp; dateFilter.month !== '') {\n   298\t            const filterMonth = parseInt(dateFilter.month);\n   299\t            if (month !== filterMonth) {\n   300\t              match = false;\n   301\t            }\n   302\t          }\n   303\t          \n   304\t          return match;\n   305\t        });\n   306\t      }\n   307\t      \n   308\t      // 处理当前批次的数据\n   309\t      filteredData.forEach((row, index) =&gt; {\n   310\t        if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) return;\n   311\t        \n   312\t        const originalPhone = String(row[phoneColumnIndex]);\n   313\t        const cleanedPhone = cleanPhoneNumber(originalPhone, removeCountryCode);\n   314\t        \n   315\t        if (!cleanedPhone) return;\n   316\t        \n   317\t        if (uniquePhones.has(cleanedPhone)) {\n   318\t          // 记录重复项\n   319\t          if (!duplicates.has(cleanedPhone)) {\n   320\t            duplicates.set(cleanedPhone, [uniquePhones.get(cleanedPhone)]);\n   321\t          }\n   322\t          duplicates.get(cleanedPhone).push(startRow + index);\n   323\t        } else {\n   324\t          // 记录唯一项\n   325\t          uniquePhones.set(cleanedPhone, startRow + index);\n   326\t        }\n   327\t      });\n   328\t      \n   329\t      totalProcessed += filteredData.length;\n   330\t    }\n   331\t    \n   332\t    // 构建结果\n   333\t    const uniqueRows = [];\n   334\t    const duplicateRows = [];\n   335\t    \n   336\t    // 再次分批读取数据，获取完整行\n   337\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   338\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   339\t      \n   340\t      // 读取当前批次的数据\n   341\t      const batchRange = {\n   342\t        s: { r: startRow, c: 0 },\n   343\t        e: { r: endRow, c: range.e.c }\n   344\t      };\n   345\t      \n   346\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   347\t        header: 1, \n   348\t        defval: '',\n   349\t        blankrows: false,\n   350\t        range: batchRange\n   351\t      });\n   352\t      \n   353\t      // 处理当前批次的数据\n   354\t      jsonData.forEach((row, index) =&gt; {\n   355\t        const rowIndex = startRow + index;\n   356\t        \n   357\t        // 检查是否是唯一行\n   358\t        for (const [phone, uniqueIndex] of uniquePhones.entries()) {\n   359\t          if (uniqueIndex === rowIndex) {\n   360\t            const newRow = [...row];\n   361\t            \n   362\t            // 如果需要保持原始格式，不修改电话号码\n   363\t            if (!keepOriginalFormat) {\n   364\t              newRow[phoneColumnIndex] = phone;\n   365\t            }\n   366\t            \n   367\t            uniqueRows.push(newRow);\n   368\t            break;\n   369\t          }\n   370\t        }\n   371\t        \n   372\t        // 检查是否是重复行\n   373\t        for (const [phone, indices] of duplicates.entries()) {\n   374\t          if (indices.includes(rowIndex)) {\n   375\t            duplicateRows.push(row);\n   376\t            break;\n   377\t          }\n   378\t        }\n   379\t      });\n   380\t      \n   381\t      // 限制结果数量，避免内存溢出\n   382\t      if (uniqueRows.length &gt; 50000 || duplicateRows.length &gt; 50000) {\n   383\t        console.log('结果数量过多，提前结束处理');\n   384\t        break;\n   385\t      }\n   386\t    }\n   387\t    \n   388\t    console.log(`去重完成: 唯一号码 ${uniqueRows.length} 个, 重复号码 ${duplicateRows.length} 个`);\n   389\t    res.json({\n   390\t      headers: headers,\n   391\t      uniqueRows: uniqueRows,\n   392\t      duplicateRows: duplicateRows,\n   393\t      totalProcessed: totalProcessed\n   394\t    });\n   395\t  } catch (error) {\n   396\t    console.error('去重处理出错:', error);\n   397\t    res.status(500).json({ error: error.message });\n   398\t  }\n   399\t});\n   400\t\n   401\t// API 路由: 关键词搜索\n   402\tapp.post('/api/search-excel', (req, res) =&gt; {\n   403\t  console.log('收到搜索请求:', req.body);\n   404\t  try {\n   405\t    const { filePath, sheetName, searchColumnIndex, keyword, caseSensitive, exactMatch, deduplicateResults, phoneColumnIndex } = req.body;\n   406\t    \n   407\t    if (!filePath || !sheetName) {\n   408\t      return res.status(400).json({ error: '缺少必要参数' });\n   409\t    }\n   410\t    \n   411\t    // 读取Excel文件 - 优化读取选项\n   412\t    const workbook = XLSX.readFile(filePath, {\n   413\t      cellFormula: false,\n   414\t      cellStyles: false,\n   415\t      cellDates: true,\n   416\t      dense: true,\n   417\t      sheetStubs: true,\n   418\t      type: 'binary',\n   419\t      rawHTML: false,\n   420\t      cellHTML: false\n   421\t    });\n   422\t    \n   423\t    if (!workbook.SheetNames.includes(sheetName)) {\n   424\t      return res.status(400).json({ error: '工作表不存在' });\n   425\t    }\n   426\t    \n   427\t    // 获取工作表\n   428\t    const worksheet = workbook.Sheets[sheetName];\n   429\t    \n   430\t    // 获取表头\n   431\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   432\t      header: 1, \n   433\t      defval: '',\n   434\t      blankrows: false,\n   435\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   436\t    })[0] || [];\n   437\t    \n   438\t    // 准备搜索关键词\n   439\t    let searchKeyword = keyword;\n   440\t    if (!caseSensitive) {\n   441\t      searchKeyword = searchKeyword.toLowerCase();\n   442\t    }\n   443\t    \n   444\t    // 分批处理数据\n   445\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   446\t    const matchedRows = [];\n   447\t    const uniquePhones = new Set();\n   448\t    \n   449\t    // 获取工作表范围\n   450\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   451\t    const totalRows = range.e.r;\n   452\t    \n   453\t    // 分批处理数据\n   454\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   455\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   456\t      \n   457\t      // 读取当前批次的数据\n   458\t      const batchRange = {\n   459\t        s: { r: startRow, c: 0 },\n   460\t        e: { r: endRow, c: range.e.c }\n   461\t      };\n   462\t      \n   463\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   464\t        header: 1, \n   465\t        defval: '',\n   466\t        blankrows: false,\n   467\t        range: batchRange\n   468\t      });\n   469\t      \n   470\t      // 搜索匹配的行\n   471\t      jsonData.forEach(row =&gt; {\n   472\t        if (searchColumnIndex &gt;= row.length || !row[searchColumnIndex]) return;\n   473\t        \n   474\t        let cellValue = String(row[searchColumnIndex]);\n   475\t        if (!caseSensitive) {\n   476\t          cellValue = cellValue.toLowerCase();\n   477\t        }\n   478\t        \n   479\t        let isMatch = false;\n   480\t        if (exactMatch) {\n   481\t          isMatch = cellValue === searchKeyword;\n   482\t        } else {\n   483\t          isMatch = cellValue.includes(searchKeyword);\n   484\t        }\n   485\t        \n   486\t        if (isMatch) {\n   487\t          // 如果需要去重\n   488\t          if (deduplicateResults &amp;&amp; phoneColumnIndex !== undefined &amp;&amp; phoneColumnIndex &gt;= 0) {\n   489\t            if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) {\n   490\t              matchedRows.push(row);\n   491\t              return;\n   492\t            }\n   493\t            \n   494\t            const phone = cleanPhoneNumber(row[phoneColumnIndex], false);\n   495\t            if (!phone) {\n   496\t              matchedRows.push(row);\n   497\t              return;\n   498\t            }\n   499\t            \n   500\t            if (uniquePhones.has(phone)) {\n   501\t              return;\n   502\t            }\n   503\t            \n   504\t            uniquePhones.add(phone);\n   505\t          }\n   506\t          \n   507\t          matchedRows.push(row);\n   508\t          \n   509\t          // 限制结果数量，避免内存溢出\n   510\t          if (matchedRows.length &gt;= 10000) {\n   511\t            return;\n   512\t          }\n   513\t        }\n   514\t      });\n   515\t      \n   516\t      // 如果已经找到足够多的结果，提前结束\n   517\t      if (matchedRows.length &gt;= 10000) {\n   518\t        console.log('搜索结果超过10000条，提前结束搜索');\n   519\t        break;\n   520\t      }\n   521\t    }\n   522\t    \n   523\t    console.log(`搜索完成: 找到 ${matchedRows.length} 条匹配记录`);\n   524\t    res.json({\n   525\t      headers: headers,\n   526\t      matchedRows: matchedRows\n   527\t    });\n   528\t  } catch (error) {\n   529\t    console.error('搜索出错:', error);\n   530\t    res.status(500).json({ error: error.message });\n   531\t  }\n   532\t});\n   533\t\n   534\t// API 路由: 多电话号码分离\n   535\tapp.post('/api/split-phones', (req, res) =&gt; {\n   536\t  console.log('收到分离请求:', req.body);\n   537\t  try {\n   538\t    const { filePath, sheetName, phoneColumnIndex, nameColumnIndex, removeCountryCode, deduplicateResults, dateFilter } = req.body;\n   539\t    \n   540\t    if (!filePath || !sheetName) {\n   541\t      return res.status(400).json({ error: '缺少必要参数' });\n   542\t    }\n   543\t    \n   544\t    // 检查文件是否存在\n   545\t    if (!fs.existsSync(filePath)) {\n   546\t      console.log('文件不存在:', filePath);\n   547\t      return res.status(400).json({ error: '文件不存在' });\n   548\t    }\n   549\t    \n   550\t    // 读取Excel文件 - 优化读取选项\n   551\t    const workbook = XLSX.readFile(filePath, {\n   552\t      cellFormula: false,\n   553\t      cellStyles: false,\n   554\t      cellDates: true,\n   555\t      dense: true,\n   556\t      sheetStubs: true,\n   557\t      type: 'binary',\n   558\t      rawHTML: false,\n   559\t      cellHTML: false\n   560\t    });\n   561\t    \n   562\t    if (!workbook.SheetNames.includes(sheetName)) {\n   563\t      return res.status(400).json({ error: '工作表不存在' });\n   564\t    }\n   565\t    \n   566\t    // 获取工作表\n   567\t    const worksheet = workbook.Sheets[sheetName];\n   568\t    \n   569\t    // 获取表头\n   570\t    const headers = XLSX.utils.sheet_to_json(worksheet, { \n   571\t      header: 1, \n   572\t      defval: '',\n   573\t      blankrows: false,\n   574\t      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行\n   575\t    })[0] || [];\n   576\t    \n   577\t    // 分批处理数据\n   578\t    const BATCH_SIZE = 5000; // 每批处理的行数\n   579\t    const splitRows = [];\n   580\t    const uniquePhones = new Set();\n   581\t    let totalProcessed = 0;\n   582\t    \n   583\t    // 获取工作表范围\n   584\t    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');\n   585\t    const totalRows = range.e.r;\n   586\t    \n   587\t    console.log(`处理 ${totalRows} 行数据进行电话号码分离`);\n   588\t    \n   589\t    // 分批处理数据\n   590\t    for (let startRow = 1; startRow &lt;= totalRows; startRow += BATCH_SIZE) {\n   591\t      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);\n   592\t      \n   593\t      // 读取当前批次的数据\n   594\t      const batchRange = {\n   595\t        s: { r: startRow, c: 0 },\n   596\t        e: { r: endRow, c: range.e.c }\n   597\t      };\n   598\t      \n   599\t      const jsonData = XLSX.utils.sheet_to_json(worksheet, { \n   600\t        header: 1, \n   601\t        defval: '',\n   602\t        blankrows: false,\n   603\t        range: batchRange\n   604\t      });\n   605\t      \n   606\t      // 应用日期筛选\n   607\t      let filteredData = jsonData;\n   608\t      if (dateFilter &amp;&amp; dateFilter.enabled &amp;&amp; dateFilter.columnIndex &gt;= 0) {\n   609\t        filteredData = jsonData.filter(row =&gt; {\n   610\t          if (dateFilter.columnIndex &gt;= row.length || !row[dateFilter.columnIndex]) {\n   611\t            return false;\n   612\t          }\n   613\t          \n   614\t          const date = parseDate(row[dateFilter.columnIndex]);\n   615\t          if (!date) return false;\n   616\t          \n   617\t          const year = date.getFullYear();\n   618\t          const month = date.getMonth() + 1;\n   619\t          \n   620\t          let match = true;\n   621\t          if (dateFilter.year &amp;&amp; dateFilter.year !== '') {\n   622\t            const filterYear = parseInt(dateFilter.year);\n   623\t            if (year !== filterYear) {\n   624\t              match = false;\n   625\t            }\n   626\t          }\n   627\t          \n   628\t          if (match &amp;&amp; dateFilter.month &amp;&amp; dateFilter.month !== '') {\n   629\t            const filterMonth = parseInt(dateFilter.month);\n   630\t            if (month !== filterMonth) {\n   631\t              match = false;\n   632\t            }\n   633\t          }\n   634\t          \n   635\t          return match;\n   636\t        });\n   637\t      }\n   638\t      \n   639\t      totalProcessed += filteredData.length;\n   640\t      \n   641\t      // 分离电话号码\n   642\t      filteredData.forEach(row =&gt; {\n   643\t        if (phoneColumnIndex &gt;= row.length || !row[phoneColumnIndex]) return;\n   644\t        \n   645\t        const phoneStr = String(row[phoneColumnIndex]);\n   646\t        \n   647\t        // 使用多种分隔符分割电话号码\n   648\t        const phones = phoneStr.split(/[,;，；\\s\\/\\|\\n\\r]+/);\n   649\t        \n   650\t        // 处理可能连在一起的多个电话号码\n   651\t        let processedPhones = [];\n   652\t        phones.forEach(phone =&gt; {\n   653\t          // 如果一个字段包含多个11位数字，可能是多个电话号码连在一起\n   654\t          if (phone.length &gt; 11) {\n   655\t            // 尝试按照11位一组分割\n   656\t            const cleanedLongPhone = phone.replace(/\\D/g, '');\n   657\t            if (cleanedLongPhone.length &gt;= 11) {\n   658\t              // 每11位分割一次\n   659\t              for (let i = 0; i &lt; cleanedLongPhone.length; i += 11) {\n   660\t                if (i + 11 &lt;= cleanedLongPhone.length) {\n   661\t                  const singlePhone = cleanedLongPhone.substring(i, i + 11);\n   662\t                  processedPhones.push(singlePhone);\n   663\t                } else if (cleanedLongPhone.length - i &gt;= 8) {\n   664\t                  // 如果剩余数字大于等于8位，也认为是一个有效电话号码\n   665\t                  const singlePhone = cleanedLongPhone.substring(i);\n   666\t                  processedPhones.push(singlePhone);\n   667\t                }\n   668\t              }\n   669\t            } else {\n   670\t              processedPhones.push(phone);\n   671\t            }\n   672\t          } else {\n   673\t            processedPhones.push(phone);\n   674\t          }\n   675\t        });\n   676\t        \n   677\t        processedPhones.forEach(phone =&gt; {\n   678\t          if (!phone) return;\n   679\t          \n   680\t          const cleanedPhone = cleanPhoneNumber(phone, removeCountryCode);\n   681\t          if (!cleanedPhone || cleanedPhone.length &lt; 8) {\n   682\t            return; // 跳过无效电话号码\n   683\t          }\n   684\t          \n   685\t          // 如果需要去重并且电话号码已存在，则跳过\n   686\t          if (deduplicateResults &amp;&amp; uniquePhones.has(cleanedPhone)) {\n   687\t            return;\n   688\t          }\n   689\t          \n   690\t          // 创建新行\n   691\t          const newRow = [...row];\n   692\t          newRow[phoneColumnIndex] = cleanedPhone;\n   693\t          \n   694\t          splitRows.push(newRow);\n   695\t          \n   696\t          if (deduplicateResults) {\n   697\t            uniquePhones.add(cleanedPhone);\n   698\t          }\n   699\t          \n   700\t          // 限制结果数量，避免内存溢出\n   701\t          if (splitRows.length &gt;= 100000) {\n   702\t            return;\n   703\t          }\n   704\t        });\n   705\t        \n   706\t        // 如果已经找到足够多的结果，提前结束\n   707\t        if (splitRows.length &gt;= 100000) {\n   708\t          return;\n   709\t        }\n   710\t      });\n   711\t      \n   712\t      // 如果已经找到足够多的结果，提前结束\n   713\t      if (splitRows.length &gt;= 100000) {\n   714\t        console.log('分离结果超过100000条，提前结束处理');\n   715\t        break;\n   716\t      }\n   717\t    }\n   718\t    \n   719\t    console.log(`分离完成: 共生成 ${splitRows.length} 行数据`);\n   720\t    res.json({\n   721\t      headers: headers,\n   722\t      splitRows: splitRows,\n   723\t      totalProcessed: totalProcessed\n   724\t    });\n   725\t  } catch (error) {\n   726\t    console.error('分离电话号码出错:', error);\n   727\t    res.status(500).json({ error: error.message });\n   728\t  }\n   729\t});\n   730\t\n   731\t// API 路由: 导出 Excel\n   732\tapp.post('/api/export-excel', (req, res) =&gt; {\n   733\t    console.log('收到导出请求');\n   734\t    try {\n   735\t      const { headers, data, sheetName } = req.body;\n   736\t      \n   737\t      if (!headers || !data) {\n   738\t        return res.status(400).json({ error: '缺少必要参数' });\n   739\t      }\n   740\t      \n   741\t      // 限制导出数据量\n   742\t      const MAX_EXPORT_ROWS = 100000;\n   743\t      const limitedData = data.length &gt; MAX_EXPORT_ROWS ? data.slice(0, MAX_EXPORT_ROWS) : data;\n   744\t      \n   745\t      if (data.length &gt; MAX_EXPORT_ROWS) {\n   746\t        console.log(`警告: 导出数据被限制为 ${MAX_EXPORT_ROWS} 行，原始数据有 ${data.length} 行`);\n   747\t      }\n   748\t      \n   749\t      // 创建工作簿\n   750\t      const workbook = XLSX.utils.book_new();\n   751\t      \n   752\t      // 准备数据（包括表头）\n   753\t      const wsData = [headers, ...limitedData];\n   754\t      \n   755\t      // 创建工作表\n   756\t      const worksheet = XLSX.utils.aoa_to_sheet(wsData);\n   757\t      \n   758\t      // 将工作表添加到工作簿\n   759\t      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName || 'Sheet1');\n   760\t      \n   761\t      // 生成临时文件路径\n   762\t      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);\n   763\t      \n   764\t      // 写入文件\n   765\t      XLSX.writeFile(workbook, outputPath, { compression: true }); // 启用压缩\n   766\t      \n   767\t      // 检查文件是否成功创建\n   768\t      if (!fs.existsSync(outputPath)) {\n   769\t        throw new Error('创建Excel文件失败');\n   770\t      }\n   771\t      \n   772\t      // 检查文件大小\n   773\t      const stats = fs.statSync(outputPath);\n   774\t      if (stats.size === 0) {\n   775\t        throw new Error('生成的Excel文件为空');\n   776\t      }\n   777\t      \n   778\t      console.log(`Excel文件已创建: ${outputPath}, 大小: ${stats.size} 字节`);\n   779\t      \n   780\t      // 设置响应头，让浏览器下载文件\n   781\t      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\n   782\t      res.setHeader('Content-Disposition', `attachment; filename=\&quot;${encodeURIComponent(sheetName || 'export')}.xlsx\&quot;`);\n   783\t      res.setHeader('Content-Length', stats.size);\n   784\t      \n   785\t      // 创建文件读取流\n   786\t      const fileStream = fs.createReadStream(outputPath);\n   787\t      \n   788\t      // 处理\n   789\t      fileStream.on('error', (error) =&gt; {\n   790\t        console.error('文件流错误:', error);\n   791\t        // 如果响应头尚未发送，发送错误响应\n   792\t        if (!res.headersSent) {\n   793\t          res.status(500).json({ error: '读取导出文件时出错' });\n   794\t        } else {\n   795\t          // 否则结束响应\n   796\t          res.end();\n   797\t        }\n   798\t        \n   799\t        // 尝试删除临时文件\n   800\t        try {\n   801\t          fs.unlinkSync(outputPath);\n   802\t        } catch (unlinkError) {\n   803\t          console.error('删除临时文件出错:', unlinkError);\n   804\t        }\n   805\t      });\n   806\t      \n   807\t      // 管道连接到响应\n   808\t      fileStream.pipe(res).on('finish', () =&gt; {\n   809\t        console.log('文件传输完成');\n   810\t        \n   811\t        // 删除临时文件\n   812\t        fs.unlink(outputPath, (unlinkErr) =&gt; {\n   813\t          if (unlinkErr) {\n   814\t            console.error('删除临时文件出错:', unlinkErr);\n   815\t          }\n   816\t        });\n   817\t      });\n   818\t    } catch (error) {\n   819\t      console.error('导出Excel出错:', error);\n   820\t      res.status(500).json({ error: error.message });\n   821\t      \n   822\t      // 尝试删除可能创建的临时文件\n   823\t      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);\n   824\t      if (fs.existsSync(outputPath)) {\n   825\t        try {\n   826\t          fs.unlinkSync(outputPath);\n   827\t        } catch (unlinkError) {\n   828\t          console.error('删除临时文件出错:', unlinkError);\n   829\t        }\n   830\t      }\n   831\t    }\n   832\t  });\n   833\t\n   834\t// 启动服务器\n   835\tapp.listen(port, () =&gt; {\n   836\t  const localIP = getLocalIP();\n   837\t  console.log(`服务器已启动:`);\n   838\t  console.log(`- 本地访问: http://localhost:${port}`);\n   839\t  console.log(`- 局域网访问: http://${localIP}:${port}`);\n   840\t});\nTotal lines in file: 840\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ec1eeba9-c59c-415a-9aef-6692197da2e9;toolu_vrtx_01JLsQS6P7p2NSqmdwGa38mS&quot;:{&quot;requestId&quot;:&quot;ec1eeba9-c59c-415a-9aef-6692197da2e9&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01JLsQS6P7p2NSqmdwGa38mS&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in public, excluding hidden items:\npublic/css\npublic/css/bootstrap\npublic/css/style.css\npublic/index.html\npublic/js\npublic/js/bootstrap\npublic/js/error-handler.js\npublic/js/main.js\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;61058cc5-056b-4172-935b-e7fa917e02fd;toolu_vrtx_01J2HZh1XZDsDCmjJxeE6GEh&quot;:{&quot;requestId&quot;:&quot;61058cc5-056b-4172-935b-e7fa917e02fd&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01J2HZh1XZDsDCmjJxeE6GEh&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on public/index.html:\n     1\t&lt;!DOCTYPE html&gt;\n     2\t&lt;html lang=\&quot;zh-CN\&quot;&gt;\n     3\t&lt;head&gt;\n     4\t  &lt;meta charset=\&quot;UTF-8\&quot;&gt;\n     5\t  &lt;meta name=\&quot;viewport\&quot; content=\&quot;width=device-width, initial-scale=1.0\&quot;&gt;\n     6\t  &lt;title&gt;Excel 电话号码处理工具&lt;/title&gt;\n     7\t  &lt;script src=\&quot;/js/error-handler.js\&quot;&gt;&lt;/script&gt;\n     8\t  &lt;!-- 使用本地 Bootstrap CSS --&gt;\n     9\t  &lt;link rel=\&quot;stylesheet\&quot; href=\&quot;/css/bootstrap/bootstrap.min.css\&quot;&gt;\n    10\t  &lt;link rel=\&quot;stylesheet\&quot; href=\&quot;/css/style.css\&quot;&gt;\n    11\t  &lt;!-- 添加 favicon 链接 --&gt;\n    12\t  &lt;link rel=\&quot;icon\&quot; href=\&quot;data:,\&quot;&gt;\n    13\t  &lt;!-- 内联样式，确保基本样式即使外部CSS加载失败也能显示 --&gt;\n    14\t  &lt;style&gt;\n    15\t    body {\n    16\t      background-color: #f8f9fa;\n    17\t      font-family: Arial, sans-serif;\n    18\t      padding: 20px;\n    19\t    }\n    20\t    .container {\n    21\t      max-width: 1200px;\n    22\t      margin: 0 auto;\n    23\t    }\n    24\t    .card {\n    25\t      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    26\t      border: none;\n    27\t      margin-bottom: 20px;\n    28\t      background-color: white;\n    29\t      border-radius: 5px;\n    30\t    }\n    31\t    .card-body {\n    32\t      padding: 20px;\n    33\t    }\n    34\t    .btn {\n    35\t      margin-right: 10px;\n    36\t    }\n    37\t    .form-label {\n    38\t      font-weight: 500;\n    39\t    }\n    40\t    .alert {\n    41\t      padding: 10px;\n    42\t      border-radius: 4px;\n    43\t      margin-top: 10px;\n    44\t    }\n    45\t    .alert-info {\n    46\t      background-color: #d1ecf1;\n    47\t      border-color: #bee5eb;\n    48\t      color: #0c5460;\n    49\t    }\n    50\t    .alert-success {\nTotal lines in file: 300\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;b0d7c840-f3a9-4a8a-a4b8-0a953ff33ab0;toolu_vrtx_01Cbnk5gj8qMh6JuqqQHuCDj&quot;:{&quot;requestId&quot;:&quot;b0d7c840-f3a9-4a8a-a4b8-0a953ff33ab0&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01Cbnk5gj8qMh6JuqqQHuCDj&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on public/js/main.js:\n     1\t// 全局变量\n     2\tlet currentFilePath = '';\n     3\tlet currentSheetName = '';\n     4\tlet excelData = null;\n     5\tlet searchResults = null;\n     6\tlet splitResults = null;\n     7\tlet dedupResults = null;\n     8\t\n     9\t// 防止 MutationObserver 错误\n    10\tdocument.addEventListener('DOMContentLoaded', function() {\n    11\t  // 捕获可能的 MutationObserver 错误\n    12\t  window.addEventListener('error', function(event) {\n    13\t    if (event.error &amp;&amp; event.error.message &amp;&amp; event.error.message.includes('MutationObserver')) {\n    14\t      console.warn('捕获到 MutationObserver 错误:', event.error.message);\n    15\t      event.preventDefault();\n    16\t      return true;\n    17\t    }\n    18\t  });\n    19\t});\n    20\t\n    21\t// 在 main.js 文件开头添加\n    22\t// 额外的错误处理\n    23\tconsole.log('main.js 加载中...');\n    24\t\n    25\t// 全局错误处理函数\n    26\twindow.onerror = function(message, source, lineno, colno, error) {\n    27\t  console.warn('全局错误:', message, 'at', source, lineno, colno);\n    28\t  // 返回 true 表示错误已处理\n    29\t  if (message &amp;&amp; (message.includes('MutationObserver') || message.includes('observe'))) {\n    30\t    return true;\n    31\t  }\n    32\t  return false;\n    33\t};\n    34\t\n    35\t// 其余 main.js 代码保持不变\n    36\t\n    37\t// DOM 元素\n    38\tdocument.addEventListener('DOMContentLoaded', function() {\n    39\t  // 初始化日期选择器\n    40\t  initializeDateSelectors();\n    41\t  \n    42\t  // 初始化事件监听器\n    43\t  initializeEventListeners();\n    44\t});\n    45\t\n    46\t// 初始化日期选择器\n    47\tfunction initializeDateSelectors() {\n    48\t  const yearSelectors = ['dedupYear', 'splitYear'];\n    49\t  const monthSelectors = ['dedupMonth', 'splitMonth'];\n    50\t  \n    51\t  // 初始化年份选择器\n    52\t  yearSelectors.forEach(id =&gt; {\n    53\t    const selector = document.getElementById(id);\n    54\t    if (selector) {\n    55\t      // 添加空选项\n    56\t      const emptyOption = document.createElement('option');\n    57\t      emptyOption.value = '';\n    58\t      emptyOption.textContent = '-- 选择年份 --';\n    59\t      selector.appendChild(emptyOption);\n    60\t      \n    61\t      // 添加年份选项（当前年份前后5年）\n    62\t      const currentYear = new Date().getFullYear();\n    63\t      for (let year = currentYear - 10; year &lt;= currentYear + 15; year++) {\n    64\t        const option = document.createElement('option');\n    65\t        option.value = year;\n    66\t        option.textContent = year;\n    67\t        selector.appendChild(option);\n    68\t      }\n    69\t    }\n    70\t  });\n    71\t  \n    72\t  // 初始化月份选择器\n    73\t  monthSelectors.forEach(id =&gt; {\n    74\t    const selector = document.getElementById(id);\n    75\t    if (selector) {\n    76\t      // 添加空选项\n    77\t      const emptyOption = document.createElement('option');\n    78\t      emptyOption.value = '';\n    79\t      emptyOption.textContent = '-- 选择月份 --';\n    80\t      selector.appendChild(emptyOption);\n    81\t      \n    82\t      // 添加月份选项\n    83\t      for (let month = 1; month &lt;= 12; month++) {\n    84\t        const option = document.createElement('option');\n    85\t        option.value = month;\n    86\t        option.textContent = month;\n    87\t        selector.appendChild(option);\n    88\t      }\n    89\t    }\n    90\t  });\n    91\t  \n    92\t  // 初始化日期筛选选项显示/隐藏\n    93\t  const dateFilterCheckboxes = [\n    94\t    { checkbox: 'dedupEnableDateFilter', options: 'dedupDateFilterOptions' },\n    95\t    { checkbox: 'splitEnableDateFilter', options: 'splitDateFilterOptions' }\n    96\t  ];\n    97\t  \n    98\t  dateFilterCheckboxes.forEach(item =&gt; {\n    99\t    const checkbox = document.getElementById(item.checkbox);\n   100\t    const options = document.getElementById(item.options);\nTotal lines in file: 969\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;6c9063c4-3103-46f3-a15a-03948b410032;toolu_vrtx_01C5NdBf9CYBjCzDznUSGPTo&quot;:{&quot;requestId&quot;:&quot;6c9063c4-3103-46f3-a15a-03948b410032&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01C5NdBf9CYBjCzDznUSGPTo&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in uploads, excluding hidden items:\n(empty directory)\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;088fe792-41aa-404a-99e5-1a31c14826ed;toolu_vrtx_01R64LE1dSg7RoQFjz3AX7ZU&quot;:{&quot;requestId&quot;:&quot;088fe792-41aa-404a-99e5-1a31c14826ed&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01R64LE1dSg7RoQFjz3AX7ZU&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on public/index.html:\n   100\t              &lt;/div&gt;\n   101\t              \n   102\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   103\t                &lt;label for=\&quot;dedupSheet\&quot; class=\&quot;form-label\&quot;&gt;选择工作表&lt;/label&gt;\n   104\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupSheet\&quot; disabled&gt;&lt;/select&gt;\n   105\t              &lt;/div&gt;\n   106\t              \n   107\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   108\t                &lt;label for=\&quot;dedupPhoneColumn\&quot; class=\&quot;form-label\&quot;&gt;选择电话号码列&lt;/label&gt;\n   109\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupPhoneColumn\&quot; disabled&gt;&lt;/select&gt;\n   110\t              &lt;/div&gt;\n   111\t              \n   112\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   113\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupRemoveCountryCode\&quot;&gt;\n   114\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupRemoveCountryCode\&quot;&gt;移除国家代码 (+86/86)&lt;/label&gt;\n   115\t              &lt;/div&gt;\n   116\t              \n   117\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   118\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupKeepOriginalFormat\&quot;&gt;\n   119\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupKeepOriginalFormat\&quot;&gt;保持原始格式&lt;/label&gt;\n   120\t              &lt;/div&gt;\n   121\t              \n   122\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   123\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;dedupEnableDateFilter\&quot;&gt;\n   124\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;dedupEnableDateFilter\&quot;&gt;启用日期筛选&lt;/label&gt;\n   125\t              &lt;/div&gt;\n   126\t              \n   127\t              &lt;div id=\&quot;dedupDateFilterOptions\&quot; style=\&quot;display: none;\&quot;&gt;\n   128\t                &lt;div class=\&quot;mb-3\&quot;&gt;\n   129\t                  &lt;label for=\&quot;dedupDateColumn\&quot; class=\&quot;form-label\&quot;&gt;选择日期列&lt;/label&gt;\n   130\t                  &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupDateColumn\&quot;&gt;&lt;/select&gt;\n   131\t                &lt;/div&gt;\n   132\t                \n   133\t                &lt;div class=\&quot;row\&quot;&gt;\n   134\t                  &lt;div class=\&quot;col-md-6 mb-3\&quot;&gt;\n   135\t                    &lt;label for=\&quot;dedupYear\&quot; class=\&quot;form-label\&quot;&gt;年份&lt;/label&gt;\n   136\t                    &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupYear\&quot;&gt;&lt;/select&gt;\n   137\t                  &lt;/div&gt;\n   138\t                  \n   139\t                  &lt;div class=\&quot;col-md-6 mb-3\&quot;&gt;\n   140\t                    &lt;label for=\&quot;dedupMonth\&quot; class=\&quot;form-label\&quot;&gt;月份&lt;/label&gt;\n   141\t                    &lt;select class=\&quot;form-select\&quot; id=\&quot;dedupMonth\&quot;&gt;&lt;/select&gt;\n   142\t                  &lt;/div&gt;\n   143\t                &lt;/div&gt;\n   144\t              &lt;/div&gt;\n   145\t              \n   146\t              &lt;button type=\&quot;submit\&quot; class=\&quot;btn btn-primary\&quot; id=\&quot;dedupButton\&quot; disabled&gt;去重处理&lt;/button&gt;\n   147\t              &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-success\&quot; id=\&quot;dedupExportButton\&quot; disabled&gt;导出结果&lt;/button&gt;\n   148\t            &lt;/form&gt;\n   149\t            \n   150\t            &lt;div class=\&quot;mt-3\&quot; id=\&quot;dedupResult\&quot;&gt;&lt;/div&gt;\n   151\t            &lt;div class=\&quot;mt-3\&quot; id=\&quot;dedupStatus\&quot;&gt;&lt;/div&gt;\n   152\t          &lt;/div&gt;\n   153\t        &lt;/div&gt;\n   154\t      &lt;/div&gt;\n   155\t      \n   156\t      &lt;!-- 关键词搜索标签页 --&gt;\n   157\t      &lt;div class=\&quot;tab-pane fade\&quot; id=\&quot;search\&quot; role=\&quot;tabpanel\&quot; aria-labelledby=\&quot;search-tab\&quot;&gt;\n   158\t        &lt;div class=\&quot;card mt-3\&quot;&gt;\n   159\t          &lt;div class=\&quot;card-body\&quot;&gt;\n   160\t            &lt;!-- &lt;h5 class=\&quot;card-title\&quot;&gt;关键词搜索&lt;/h5&gt; --&gt;\n   161\t            \n   162\t            &lt;form id=\&quot;searchForm\&quot;&gt;\n   163\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   164\t                &lt;label for=\&quot;searchFile\&quot; class=\&quot;form-label\&quot;&gt;选择 Excel 文件&lt;/label&gt;\n   165\t                &lt;input type=\&quot;file\&quot; class=\&quot;form-control\&quot; id=\&quot;searchFile\&quot; accept=\&quot;.xlsx,.xls\&quot; required&gt;\n   166\t              &lt;/div&gt;\n   167\t              \n   168\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   169\t                &lt;label for=\&quot;searchSheet\&quot; class=\&quot;form-label\&quot;&gt;选择工作表&lt;/label&gt;\n   170\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;searchSheet\&quot; disabled&gt;&lt;/select&gt;\n   171\t              &lt;/div&gt;\n   172\t              \n   173\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   174\t                &lt;label for=\&quot;searchColumn\&quot; class=\&quot;form-label\&quot;&gt;选择搜索列&lt;/label&gt;\n   175\t                &lt;select class=\&quot;form-select\&quot; id=\&quot;searchColumn\&quot; disabled&gt;&lt;/select&gt;\n   176\t              &lt;/div&gt;\n   177\t              \n   178\t              &lt;div class=\&quot;mb-3\&quot;&gt;\n   179\t                &lt;label for=\&quot;searchKeyword\&quot; class=\&quot;form-label\&quot;&gt;搜索关键词&lt;/label&gt;\n   180\t                &lt;input type=\&quot;text\&quot; class=\&quot;form-control\&quot; id=\&quot;searchKeyword\&quot; required&gt;\n   181\t              &lt;/div&gt;\n   182\t              \n   183\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   184\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchCaseSensitive\&quot;&gt;\n   185\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchCaseSensitive\&quot;&gt;区分大小写&lt;/label&gt;\n   186\t              &lt;/div&gt;\n   187\t              \n   188\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   189\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchExactMatch\&quot;&gt;\n   190\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchExactMatch\&quot;&gt;精确匹配&lt;/label&gt;\n   191\t              &lt;/div&gt;\n   192\t              \n   193\t              &lt;div class=\&quot;mb-3 form-check\&quot;&gt;\n   194\t                &lt;input type=\&quot;checkbox\&quot; class=\&quot;form-check-input\&quot; id=\&quot;searchDeduplicateResults\&quot;&gt;\n   195\t                &lt;label class=\&quot;form-check-label\&quot; for=\&quot;searchDeduplicateResults\&quot;&gt;对结果去重&lt;/label&gt;\n   196\t              &lt;/div&gt;\n   197\t              \n   198\t              &lt;button type=\&quot;submit\&quot; class=\&quot;btn btn-primary\&quot; id=\&quot;searchButton\&quot; disabled&gt;搜索&lt;/button&gt;\n   199\t              &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-success\&quot; id=\&quot;searchExportButton\&quot; disabled&gt;导出结果&lt;/button&gt;\n   200\t            &lt;/form&gt;\nTotal lines in file: 300\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;b5b6cd5c-4f66-4b46-ac2d-8ed9a3b6b208&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>